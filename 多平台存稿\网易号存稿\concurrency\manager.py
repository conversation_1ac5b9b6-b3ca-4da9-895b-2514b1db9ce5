"""
并发管理器模块 - 负责管理视频级并发处理
重构为视频级并发架构，实现真正的高性能并发处理
"""

import os
import time
import queue
import random
import threading
import traceback
from typing import List, Dict, Any, Optional, Callable, Tuple
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from ..draft.processor import DraftProcessor


class BrowserPool:
    """浏览器实例池管理器"""

    def __init__(self, pool_size: int = 3, headless_mode: bool = False, log_callback: Callable = None):
        """
        初始化浏览器池

        Args:
            pool_size: 浏览器池大小
            headless_mode: 是否使用无头模式
            log_callback: 日志回调函数
        """
        self.pool_size = pool_size
        self.headless_mode = headless_mode
        self.log_callback = log_callback
        self.browsers = queue.Queue()
        self.lock = threading.Lock()
        self.active_browsers = set()

        # 初始化浏览器池
        self._initialize_pool()

    def _initialize_pool(self):
        """初始化浏览器池"""
        try:
            from ..browser.driver import DriverManager

            for i in range(self.pool_size):
                driver_manager = DriverManager(self.log_callback)
                driver = driver_manager.create_driver(self.headless_mode)
                if driver:
                    self.browsers.put({
                        'driver': driver,
                        'driver_manager': driver_manager,
                        'id': f'browser_{i}',
                        'in_use': False
                    })
                    self.log(f"浏览器池: 创建浏览器实例 {i+1}/{self.pool_size}")
        except Exception as e:
            self.log(f"初始化浏览器池失败: {str(e)}")

    def get_browser(self) -> Optional[Dict]:
        """获取可用的浏览器实例"""
        try:
            browser_info = self.browsers.get(timeout=30)  # 30秒超时
            with self.lock:
                browser_info['in_use'] = True
                self.active_browsers.add(browser_info['id'])
            return browser_info
        except queue.Empty:
            self.log("获取浏览器实例超时")
            return None

    def return_browser(self, browser_info: Dict):
        """归还浏览器实例到池中"""
        if browser_info and browser_info.get('driver'):
            with self.lock:
                browser_info['in_use'] = False
                self.active_browsers.discard(browser_info['id'])
            self.browsers.put(browser_info)

    def cleanup(self):
        """清理浏览器池"""
        self.log("正在清理浏览器池...")

        # 清理队列中的浏览器
        while not self.browsers.empty():
            try:
                browser_info = self.browsers.get_nowait()
                if browser_info and browser_info.get('driver'):
                    browser_info['driver'].quit()
            except queue.Empty:
                break
            except Exception as e:
                self.log(f"清理浏览器时出错: {str(e)}")

        self.log("浏览器池清理完成")

    def log(self, message: str):
        """记录日志"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)


class VideoTask:
    """视频处理任务"""

    def __init__(self, account: str, video_path: str, cover_path: str = None):
        self.account = account
        self.video_path = video_path
        self.cover_path = cover_path
        self.task_id = f"{account}_{os.path.basename(video_path)}"


class ConcurrentManager:
    """并发管理器类，负责管理视频级并发处理"""

    def __init__(self,
                 account_dir: str,
                 processed_dir: str,
                 processed_covers_dir: str,
                 archive_completed: bool = True,
                 headless_mode: bool = False,
                 draft_limit: int = 0,
                 loop_limit: int = 0,
                 log_callback: Callable = None,
                 screenshots_dir: str = None,
                 max_workers: int = 10,  # 增加默认工作线程数以支持视频级并发
                 browser_pool_size: int = 5,  # 新增浏览器池大小参数
                 random_video_allocation: bool = True):
        """
        初始化并发管理器

        Args:
            account_dir: 账号目录
            processed_dir: 已处理视频目录
            processed_covers_dir: 已处理封面目录
            archive_completed: 是否归档已完成视频
            headless_mode: 是否使用无头模式
            draft_limit: 存稿数量限制，0表示不限制
            loop_limit: 循环次数限制，0表示不限制
            log_callback: 日志回调函数
            screenshots_dir: 截图保存目录
            max_workers: 最大工作线程数（用于视频级并发）
            browser_pool_size: 浏览器池大小
            random_video_allocation: 是否随机分配视频，True为随机分配，False为顺序分配
        """
        self.account_dir = account_dir
        self.processed_dir = processed_dir
        self.processed_covers_dir = processed_covers_dir
        self.archive_completed = archive_completed
        self.headless_mode = headless_mode
        self.draft_limit = draft_limit
        self.loop_limit = loop_limit
        self.max_workers = max_workers
        self.browser_pool_size = browser_pool_size
        self.log_callback = log_callback
        self.screenshots_dir = screenshots_dir
        self.progress_callback = None
        self.random_video_allocation = random_video_allocation

        # 初始化状态
        self.is_running = False
        self.executor = None
        self.futures = {}
        self.video_queue = queue.Queue()
        self.account_progress = {}

        # 初始化浏览器池
        self.browser_pool = None

        # 视频级并发相关
        self.video_tasks = []
        self.completed_tasks = []
        self.failed_tasks = []
        self.task_lock = threading.Lock()

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def start(self, accounts: List[str]) -> bool:
        """
        开始视频级并发处理

        Args:
            accounts: 账号列表

        Returns:
            是否成功启动
        """
        if self.is_running:
            self.log("任务已在运行中")
            return False

        if not accounts:
            self.log("没有指定要处理的账号")
            return False

        self.is_running = True

        try:
            self.log(f"开始视频级并发处理，账号数量: {len(accounts)}")

            # 初始化浏览器池
            self.browser_pool = BrowserPool(
                pool_size=self.browser_pool_size,
                headless_mode=self.headless_mode,
                log_callback=self.log_callback
            )

            # 准备视频任务
            self._prepare_video_tasks(accounts)

            if not self.video_tasks:
                self.log("没有找到可处理的视频任务")
                self.is_running = False
                return False

            # 初始化线程池（用于视频级并发）
            self.executor = ThreadPoolExecutor(max_workers=self.max_workers)

            # 初始化账号进度
            for account in accounts:
                self.account_progress[account] = {
                    "progress": 0,
                    "status": "等待中",
                    "last_update": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "details": "准备处理",
                    "successful_drafts": 0
                }

            # 提交视频处理任务
            self.futures = {}
            for task in self.video_tasks:
                future = self.executor.submit(self._process_video_task, task)
                self.futures[future] = task

            self.log(f"已提交 {len(self.video_tasks)} 个视频任务到线程池")

            # 启动监控线程
            threading.Thread(target=self._monitor_video_progress, daemon=True).start()

            return True

        except Exception as e:
            self.log(f"启动视频级并发处理失败: {str(e)}")
            self.is_running = False
            return False

    def stop(self) -> None:
        """停止视频级并发处理"""
        if not self.is_running:
            return

        self.is_running = False
        self.log("正在停止视频级并发处理...")

        # 关闭线程池
        if self.executor:
            self.executor.shutdown(wait=False)
            self.executor = None

        # 清理浏览器池
        if self.browser_pool:
            self.browser_pool.cleanup()

        # 清空队列
        while not self.video_queue.empty():
            try:
                self.video_queue.get_nowait()
            except:
                pass

        self.log("视频级并发处理已停止")

    def _prepare_video_tasks(self, accounts: List[str]) -> None:
        """
        准备视频任务列表

        Args:
            accounts: 账号列表
        """
        self.video_tasks = []

        try:
            # 获取所有视频文件
            video_files = self._get_all_video_files()

            if not video_files:
                self.log("没有找到可处理的视频文件")
                return

            # 为每个账号创建视频任务
            for account in accounts:
                # 检查账号Cookie是否存在
                cookie_path = os.path.join(self.account_dir, account, "cookies.json")
                if not os.path.exists(cookie_path):
                    self.log(f"账号 {account} 的Cookie文件不存在，跳过")
                    continue

                # 获取该账号可用的视频
                account_videos = self._get_available_videos_for_account(account, video_files)

                # 应用存稿限制
                if self.draft_limit > 0:
                    account_videos = account_videos[:self.draft_limit]

                # 为每个视频创建任务
                for video_path in account_videos:
                    cover_path = self._find_cover_for_video(video_path)
                    task = VideoTask(account, video_path, cover_path)
                    self.video_tasks.append(task)

            self.log(f"准备完成，共创建 {len(self.video_tasks)} 个视频任务")

        except Exception as e:
            self.log(f"准备视频任务失败: {str(e)}")
            traceback.print_exc()

    def _get_all_video_files(self) -> List[str]:
        """获取所有视频文件"""
        video_files = []

        try:
            if not os.path.exists(self.processed_dir):
                return video_files

            for file in os.listdir(self.processed_dir):
                if file.startswith('.'):
                    continue

                file_path = os.path.join(self.processed_dir, file)
                if not os.path.isfile(file_path):
                    continue

                ext = os.path.splitext(file)[1].lower()
                if ext in ['.mp4', '.mov', '.avi', '.wmv', '.flv', '.mkv']:
                    try:
                        with open(file_path, 'rb'):
                            pass
                        video_files.append(file_path)
                    except Exception as e:
                        self.log(f"无法访问文件 {file}: {str(e)}")
                        continue

            # 根据配置决定是否随机打乱
            if self.random_video_allocation:
                random.shuffle(video_files)
            else:
                video_files.sort()

        except Exception as e:
            self.log(f"获取视频文件失败: {str(e)}")

        return video_files

    def _get_available_videos_for_account(self, account: str, video_files: List[str]) -> List[str]:
        """获取账号可用的视频列表"""
        # 这里可以实现更复杂的视频分配逻辑
        # 目前简单返回所有视频
        return video_files

    def _find_cover_for_video(self, video_path: str) -> Optional[str]:
        """查找视频对应的封面文件"""
        try:
            video_name = os.path.splitext(os.path.basename(video_path))[0]
            cover_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif']

            for ext in cover_extensions:
                cover_path = os.path.join(self.processed_covers_dir, f"{video_name}{ext}")
                if os.path.exists(cover_path):
                    return cover_path

            return None
        except Exception:
            return None

    def _process_video_task(self, task: VideoTask) -> bool:
        """
        处理单个视频任务

        Args:
            task: 视频任务

        Returns:
            是否处理成功
        """
        browser_info = None

        try:
            self.log(f"开始处理视频任务: {task.account} - {os.path.basename(task.video_path)}")

            # 从浏览器池获取浏览器实例
            browser_info = self.browser_pool.get_browser()
            if not browser_info:
                self.log(f"无法获取浏览器实例，任务失败: {task.task_id}")
                return False

            driver = browser_info['driver']

            # 登录账号
            success = self._login_account(task.account, driver)
            if not success:
                self.log(f"账号登录失败: {task.account}")
                return False

            # 处理单个视频
            success = self._process_single_video_with_driver(
                task.video_path,
                task.cover_path,
                task.account,
                driver
            )

            if success:
                self.log(f"✅ 视频处理成功: {task.account} - {os.path.basename(task.video_path)}")
                with self.task_lock:
                    self.completed_tasks.append(task)
                    # 更新账号进度
                    if task.account in self.account_progress:
                        self.account_progress[task.account]["successful_drafts"] += 1
            else:
                self.log(f"❌ 视频处理失败: {task.account} - {os.path.basename(task.video_path)}")
                with self.task_lock:
                    self.failed_tasks.append(task)

            return success

        except Exception as e:
            self.log(f"处理视频任务异常: {task.task_id} - {str(e)}")
            traceback.print_exc()
            with self.task_lock:
                self.failed_tasks.append(task)
            return False

        finally:
            # 归还浏览器实例到池中
            if browser_info:
                self.browser_pool.return_browser(browser_info)

    def _login_account(self, account: str, driver) -> bool:
        """
        使用指定的浏览器实例登录账号

        Args:
            account: 账号名称
            driver: 浏览器驱动实例

        Returns:
            是否登录成功
        """
        try:
            from ..account.login import AccountLogin

            cookie_path = os.path.join(self.account_dir, account, "cookies.json")
            if not os.path.exists(cookie_path):
                return False

            # 创建账号登录实例
            account_login = AccountLogin(None, self.log_callback)

            # 使用现有的driver进行登录
            success = account_login.login_with_existing_driver(driver, cookie_path, account)

            return success

        except Exception as e:
            self.log(f"登录账号失败: {account} - {str(e)}")
            return False

    def get_progress(self) -> Dict[str, Dict[str, Any]]:
        """
        获取处理进度

        Returns:
            账号进度字典
        """
        return self.account_progress

    def set_progress_callback(self, callback: Callable) -> None:
        """
        设置进度回调函数

        Args:
            callback: 进度回调函数，接收账号名称、进度百分比和状态
        """
        self.progress_callback = callback

    def _prepare_video_queue(self) -> None:
        """准备视频队列 - 使用原始源文件中的代码"""
        try:
            # 清空队列
            while not self.video_queue.empty():
                try:
                    self.video_queue.get_nowait()
                except queue.Empty:
                    break

            # 检查目录是否存在
            if not os.path.exists(self.processed_dir):
                os.makedirs(self.processed_dir, exist_ok=True)
                self.log(f"创建视频目录: {self.processed_dir}")
                return

            # 获取所有视频文件
            video_files = []
            for file in os.listdir(self.processed_dir):
                file_path = os.path.join(self.processed_dir, file)
                # 跳过目录和非视频文件
                if os.path.isdir(file_path):
                    continue

                # 检查文件扩展名
                ext = os.path.splitext(file)[1].lower()
                if ext in ['.mp4', '.mov', '.avi', '.wmv', '.flv', '.mkv']:
                    # 检查文件是否可以访问
                    try:
                        # 尝试打开文件以确认它是可访问的
                        with open(file_path, 'rb'):
                            # 不需要读取内容，只是检查文件是否可以打开
                            pass
                        video_files.append(file_path)
                    except Exception as e:
                        self.log(f"无法访问文件 {file}: {str(e)}")
                        continue

            # 根据配置决定是否随机打乱文件顺序（合并日志输出）
            if self.random_video_allocation:
                random.shuffle(video_files)
                sort_method = "随机排序"
            else:
                video_files.sort()
                sort_method = "顺序排序"

            # 将视频文件放入队列
            for video_file in video_files:
                self.video_queue.put(video_file)

            self.log(f"📁 视频队列准备完成: {len(video_files)}个文件已{sort_method}并加入队列")

        except Exception as e:
            self.log(f"准备视频队列失败: {str(e)}")
            traceback.print_exc()

    def _process_account(self, account: str) -> bool:
        """
        处理单个账号 - 使用原始源文件中的代码

        Args:
            account: 账号名称

        Returns:
            是否成功处理
        """
        try:
            # 更新账号状态
            self._update_account_progress(account, 0, "开始处理", "正在初始化")

            # 重置当前账号的存稿成功数量为0
            current_successful_drafts = 0
            if account in self.account_progress:
                # 记录旧的存稿成功数量（仅用于日志）
                old_successful_drafts = self.account_progress[account].get("successful_drafts", 0)
                if old_successful_drafts > 0:
                    self.log(f"账号 {account} 重置存稿成功数量: {old_successful_drafts} -> 0")

                # 更新进度数据，重置存稿成功数量
                progress_data = {
                    "progress": 0,
                    "status": "开始处理",
                    "details": "正在初始化",
                    "successful_drafts": 0  # 重置为0
                }
                self._update_account_progress(account, 0, "开始处理", "正在初始化", progress_data)

            # 创建存稿处理器
            processor = DraftProcessor(
                account_dir=self.account_dir,
                processed_dir=self.processed_dir,
                processed_covers_dir=self.processed_covers_dir,
                archive_completed=self.archive_completed,
                headless_mode=self.headless_mode,
                draft_limit=self.draft_limit,
                loop_limit=self.loop_limit,
                log_callback=lambda msg: self._account_log(account, msg),
                screenshots_dir=self.screenshots_dir,
                random_video_allocation=self.random_video_allocation,
                update_draft_detail_callback=lambda acc, video_path, status, reason, screenshot, video_info:
                    self._update_draft_detail(acc, video_path, status, reason, screenshot, video_info)
            )

            # 处理账号
            result, processed_videos = processor.process_account(account)

            # 计算本次处理中的成功存稿数量（简化日志输出）
            successful_drafts_from_videos = sum(1 for video in processed_videos if video.get('status') == '成功')

            # 计算总的成功存稿数量
            if hasattr(processor, 'total_draft_count'):
                total_successful_drafts = processor.total_draft_count
            else:
                total_successful_drafts = current_successful_drafts + successful_drafts_from_videos

            # 创建包含成功存稿数量的进度数据
            progress_data = {
                "progress": 100 if result else 0,
                "status": "完成" if result else "失败",
                "details": f"处理{'完成' if result else '失败'}，成功存稿 {total_successful_drafts} 个视频",
                "successful_drafts": total_successful_drafts
            }

            # 更新账号状态，包含成功存稿数量（简化日志输出）
            if result:
                self.log(f"✅ {account} 存稿完成: {total_successful_drafts} 个视频")
                self._update_account_progress(account, 100, "完成", f"处理完成，成功存稿 {total_successful_drafts} 个视频", progress_data)
            else:
                self.log(f"❌ {account} 处理失败")
                self._update_account_progress(account, 0, "失败", f"处理失败，成功存稿 {total_successful_drafts} 个视频", progress_data)

            # 处理视频信息
            for video_info in processed_videos:
                # 添加存稿成功数量到视频信息中
                if video_info.get('status') == '成功':
                    video_info['successful_drafts'] = total_successful_drafts

                # 调用进度回调函数，传递视频信息
                if self.progress_callback:
                    self.progress_callback(account, 100 if result else 0,
                                          "完成" if result else "失败",
                                          f"视频 {os.path.basename(video_info.get('video_path', ''))} {video_info.get('status', '')}",
                                          video_info)

            # 记录成功存稿数量
            self.log(f"账号 {account} 成功存稿 {total_successful_drafts} 个视频")

            return result

        except Exception as e:
            self.log(f"处理账号 {account} 时发生错误: {str(e)}")
            self._update_account_progress(account, 0, "错误", f"发生错误: {str(e)}")
            traceback.print_exc()
            return False

    def _monitor_progress(self) -> None:
        """监控处理进度 - 使用原始源文件中的代码"""
        try:
            # 等待所有任务完成
            for future in as_completed(self.futures):
                account = self.futures[future]
                try:
                    result = future.result()
                    if result:
                        self.log(f"账号 {account} 处理完成")
                    else:
                        self.log(f"账号 {account} 处理失败")
                except Exception as e:
                    self.log(f"账号 {account} 处理异常: {str(e)}")
                    self._update_account_progress(account, 0, "异常", f"处理异常: {str(e)}")
                    traceback.print_exc()

            # 所有任务完成
            if self.is_running:
                self.log("所有账号处理完成")
                self.is_running = False

        except Exception as e:
            self.log(f"监控进度时发生错误: {str(e)}")
            traceback.print_exc()

    def _update_account_progress(self, account: str, progress: int, status: str, details: str, progress_data=None) -> None:
        """
        更新账号进度

        Args:
            account: 账号名称
            progress: 进度百分比
            status: 状态
            details: 详细信息
            progress_data: 完整的进度数据字典（可选）
        """
        # 初始化变量
        successful_drafts = 0
        old_successful_drafts = 0

        if account in self.account_progress:
            # 保留原有的成功存稿数量（如果有）
            successful_drafts = self.account_progress[account].get("successful_drafts", 0)

            # 记录更新前的值，用于日志
            old_successful_drafts = successful_drafts

            # 如果提供了完整的进度数据，使用它
            if progress_data and isinstance(progress_data, dict):
                # 更新账号进度信息，但保留原有的成功存稿数量（除非新数据中有更新的值）
                if "successful_drafts" in progress_data:
                    successful_drafts = progress_data["successful_drafts"]
                    # 记录存稿成功数量的变化
                    if successful_drafts != old_successful_drafts:
                        self.log(f"账号 {account} 存稿成功数量更新: {old_successful_drafts} -> {successful_drafts}")

                # 更新账号进度信息
                self.account_progress[account] = {
                    **progress_data,  # 使用提供的所有数据
                    "last_update": time.strftime("%Y-%m-%d %H:%M:%S"),
                }

                # 只在存稿成功数量变化或状态变化时记录日志，减少日志输出
                if successful_drafts != old_successful_drafts or status != self.account_progress[account].get("status", ""):
                    self.log(f"账号 {account} 进度数据已更新: 进度={progress}, 状态={status}, 存稿成功={successful_drafts}")
            else:
                # 使用单独的参数更新
                # 获取旧的状态（如果有）
                old_status = self.account_progress[account].get("status", "")

                self.account_progress[account] = {
                    "progress": progress,
                    "status": status,
                    "last_update": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "details": details,
                    "successful_drafts": successful_drafts
                }

                # 只在存稿成功数量变化或状态变化时记录日志，减少日志输出
                if successful_drafts != old_successful_drafts or status != old_status:
                    self.log(f"账号 {account} 基本进度已更新: 进度={progress}, 状态={status}, 存稿成功={successful_drafts}")
        else:
            # 账号不在进度字典中，创建新的进度记录
            self.account_progress[account] = {
                "progress": progress,
                "status": status,
                "last_update": time.strftime("%Y-%m-%d %H:%M:%S"),
                "details": details,
                "successful_drafts": successful_drafts
            }

            # 首次创建账号进度记录时记录日志
            self.log(f"账号 {account} 首次进度记录: 进度={progress}, 状态={status}, 存稿成功={successful_drafts}")

    def _account_log(self, account: str, message: str) -> None:
        """
        账号日志

        Args:
            account: 账号名称
            message: 日志消息
        """
        # 过滤重复和冗余的日志，减少输出
        if any(keyword in message for keyword in [
            "Cookie有效期正常", "正在添加Cookie", "Cookie添加成功",
            "处理Cookie", "使用原有域名", "Cookie已添加，刷新页面",
            "分配端口", "浏览器驱动创建成功", "正在访问网易号登录页面",
            "当前页面URL", "从JSON文件加载到", "Cookie添加完成",
            "验证Cookie是否被正确添加", "浏览器当前Cookie数量",
            "当前Cookie名称", "刷新后的URL", "主动导航后的URL",
            "正在导航到", "等待元素", "点击元素", "输入文本", "上传文件",
            "执行脚本", "切换到iframe", "获取元素文本", "已选择文件",
            "初始化驱动管理器", "初始化账号登录对象", "正在登录账号",
            "视频分配跟踪器初始化成功", "成功添加Cookie", "Cookie文件大小",
            "Cookie文件内容预览", "Cookie文件内容:", "正在加载TXT格式的Cookie文件",
            "成功使用", "编码读取文件", "文件内容长度", "文件内容预览",
            "开始解析Cookie字符串", "Cookie内容前", "成功解析为JSON格式",
            "从JSON结构中提取cookies字段", "转换字典格式cookies为列表",
            "从TXT文件加载了", "cookies字段是列表格式", "成功添加", "个Cookies",
            "找到Cookie文件", "分配端口", "当前页面URL", "等待后的页面URL",
            "Cookie登录成功", "Cookie登录失败", "期望URL", "页面被重定向到登录页面",
            "等待页面重定向"
        ]):
            # 这些是详细的技术日志，不需要在UI中显示
            return

        # 过滤重复的登录相关日志
        if any(keyword in message for keyword in [
            "开始解析Cookie字符串", "成功解析为JSON格式", "Cookie解析完成",
            "有效的cookies数量", "清除浏览器中的所有现有Cookie",
            "开始添加Cookie到浏览器", "设置默认域名", "设置mp域名",
            "正在验证登录状态", "开始监控页面变化", "第", "次检测", "次URL检测",
            "URL监控", "监控结束", "最终URL", "初始URL"
        ]):
            return

        # 只保留重要的状态变化日志
        important_keywords = [
            "登录成功", "登录失败", "视频上传成功", "视频上传失败",
            "存稿成功", "存稿失败", "已设置标题", "已设置标签",
            "开始存稿任务", "存稿任务完成", "账号处理完成"
        ]

        # 检查是否是重要日志
        is_important = any(keyword in message for keyword in important_keywords)

        # 如果是重要日志，直接显示
        if is_important:
            self.log(f"[{account}] {message}")
        else:
            # 对于非重要日志，进行更严格的过滤
            # 只有不包含任何技术关键词的日志才显示
            technical_keywords = [
                "Cookie", "浏览器", "URL", "端口", "解析", "验证", "添加",
                "设置域名", "驱动", "页面", "元素", "脚本", "iframe", "文本",
                "文件", "导航", "监控", "检测", "超时", "失败", "错误"
            ]

            # 如果消息不包含任何技术关键词，则显示
            if not any(keyword in message for keyword in technical_keywords):
                self.log(f"[{account}] {message}")

        # 根据日志内容更新进度 - 仅用于UI显示，不影响日志记录
        progress = 0
        status = ""

        if "登录成功" in message:
            progress = 10
            status = "已登录"
            self._update_account_progress(account, progress, status, "登录成功")
        elif "视频上传成功" in message:
            progress = 30
            status = "上传中"
            self._update_account_progress(account, progress, status, "视频上传成功")
        elif "已设置标题" in message:
            progress = 50
            status = "设置中"
            self._update_account_progress(account, progress, status, "已设置标题")
        elif "已设置标签" in message:
            progress = 70
            status = "设置中"
            self._update_account_progress(account, progress, status, "已设置标签")
        elif "草稿保存成功" in message or "草稿可能已保存" in message or "✅ 存稿成功！视频已保存到草稿箱" in message:
            progress = 90
            status = "已存稿"

            # 从消息中提取成功存稿数量（如果有）
            # 注意：在这里增加存稿成功数量，以便实时更新UI
            successful_drafts = 0
            if account in self.account_progress:
                successful_drafts = self.account_progress[account].get("successful_drafts", 0)
                # 增加计数，确保UI实时更新
                # 只有当消息确实是当前账号的消息时才增加计数
                # 消息格式为 "[账号名] 消息内容"，所以检查消息是否以 "[账号名]" 开头
                if message.startswith(f"[{account}]"):
                    successful_drafts += 1
                    self.log(f"账号 {account} 检测到草稿保存成功，存稿成功数量增加到: {successful_drafts}")
                else:
                    self.log(f"账号 {account} 检测到草稿保存成功消息，但不是该账号的消息，不增加计数")

            # 更新账号进度，包含成功存稿数量
            progress_data = {
                "progress": progress,
                "status": status,
                "details": f"草稿保存成功，已存稿 {successful_drafts} 个视频",
                "successful_drafts": successful_drafts
            }
            self._update_account_progress(account, progress, status, f"草稿保存成功，已存稿 {successful_drafts} 个视频", progress_data)

            # 立即调用进度回调函数，更新UI显示
            if self.progress_callback:
                # 确保传递完整的progress_data，包含successful_drafts
                self.log(f"正在调用UI回调函数，更新账号 {account} 的存稿成功数量: {successful_drafts}")
                self.progress_callback(account, progress, f"已存稿 ({successful_drafts})",
                                      f"[{account}] 草稿保存成功，已存稿 {successful_drafts} 个视频", progress_data)
        elif "视频存稿成功" in message:
            progress = 100
            status = "完成"

            # 从消息中提取成功存稿数量（如果有）
            # 注意：在这里增加存稿成功数量，以便实时更新UI
            successful_drafts = 0
            if account in self.account_progress:
                successful_drafts = self.account_progress[account].get("successful_drafts", 0)
                # 增加计数，确保UI实时更新
                # 只有当消息中包含当前账号的信息时才增加计数
                if f"[{account}]" in message or message.startswith(f"{account} "):
                    successful_drafts += 1
                    self.log(f"账号 {account} 检测到视频存稿成功，存稿成功数量增加到: {successful_drafts}")
                else:
                    self.log(f"账号 {account} 检测到视频存稿成功消息，但不是该账号的消息，不增加计数")

            # 更新账号进度，包含成功存稿数量
            progress_data = {
                "progress": progress,
                "status": status,
                "details": f"视频存稿成功，已存稿 {successful_drafts} 个视频",
                "successful_drafts": successful_drafts
            }
            self._update_account_progress(account, progress, status, f"视频存稿成功，已存稿 {successful_drafts} 个视频", progress_data)

            # 立即调用进度回调函数，更新UI显示
            if self.progress_callback:
                # 确保传递完整的progress_data，包含successful_drafts
                self.log(f"正在调用UI回调函数，更新账号 {account} 的存稿成功数量: {successful_drafts}")
                self.progress_callback(account, progress, f"完成 ({successful_drafts})",
                                      f"[{account}] 视频存稿成功，已存稿 {successful_drafts} 个视频", progress_data)
        elif "失败" in message or "错误" in message:
            # 保持当前进度，但更新状态和详情
            current_progress = self.account_progress.get(account, {}).get("progress", 0)
            progress = current_progress
            status = "警告"
            self._update_account_progress(account, progress, status, message)
        else:
            # 对于没有特定状态的日志消息，只记录日志，不更新UI状态
            # 这样可以减少频繁的"处理中"状态更新
            # 不再调用self._update_account_progress，避免生成过多的状态更新日志
            pass

        # 调用进度回调函数 - 确保所有日志消息都能传递给UI
        # 这里不再过滤消息，确保所有消息都能传递给UI
        if self.progress_callback:
            # 传递详细的日志消息，而不仅仅是状态
            # 不传递视频信息，因为这里只是日志消息
            self.progress_callback(account, progress, status, message)

    def _update_draft_detail(self, account: str, video_path: str, status: str, reason: str = "", screenshot: str = "", video_info: dict = None) -> None:
        """
        更新存稿详情数据 - 通过回调函数传递给UI

        Args:
            account: 账号名称
            video_path: 视频文件路径
            status: 状态（成功/失败）
            reason: 原因（失败时的错误信息）
            screenshot: 截图路径（失败时的截图）
            video_info: 视频信息字典（包含存稿成功数量等）
        """
        # 记录日志
        video_name = os.path.basename(video_path) if video_path else "未知视频"

        # 获取存稿成功数量
        successful_drafts = 0
        if video_info and isinstance(video_info, dict):
            successful_drafts = video_info.get("successful_drafts", 0)

        # 减少重复日志，只在关键状态变化时记录
        if status == "成功" and successful_drafts > 0:
            self.log(f"[{account}] ✅ {video_name} 存稿成功 (总存稿: {successful_drafts})")
        elif status == "失败":
            self.log(f"[{account}] ❌ {video_name} 存稿失败" + (f" - {reason}" if reason else ""))

        # 调用进度回调函数，传递视频信息
        if self.progress_callback:
            # 合并视频信息
            final_video_info = {
                "video_path": video_path,
                "status": status,
                "reason": reason,
                "screenshot": screenshot
            }

            # 如果传入了video_info，合并其内容
            if video_info and isinstance(video_info, dict):
                final_video_info.update(video_info)

            # 调用回调函数
            self.progress_callback(account,
                                  100 if status == "成功" else 0,
                                  "完成" if status == "成功" else "失败",
                                  f"视频 {video_name} {status}" + (f" - {reason}" if reason else ""),
                                  final_video_info)
