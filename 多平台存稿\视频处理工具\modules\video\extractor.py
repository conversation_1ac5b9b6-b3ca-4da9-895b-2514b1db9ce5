"""
视频帧提取模块 - 从视频中提取帧作为封面
"""

import os
import subprocess
from typing import Dict, Any, Tuple, Optional
from PIL import Image

def extract_cover(processor, 
                 video_path: str, 
                 cover_path: str, 
                 duration_seconds: float,
                 use_gpu: bool = False, 
                 gpu_device: str = "auto", 
                 memory_limit: int = 0,
                 cover_resolution: Optional[Tuple[int, int]] = None) -> Dict[str, Any]:
    """
    从视频中提取封面
    
    Args:
        processor: 视频处理器实例
        video_path: 视频文件路径
        cover_path: 封面保存路径
        duration_seconds: 视频时长
        use_gpu: 是否使用GPU加速
        gpu_device: GPU设备
        memory_limit: 内存限制(MB)
        cover_resolution: 封面分辨率，如(1280, 720)
        
    Returns:
        Dict: 提取结果，包含success、message等信息
    """
    try:
        # 提取封面（使用ffmpeg）
        cover_time = min(duration_seconds - 0.5, 5.0) if duration_seconds > 5.0 else duration_seconds / 2
        cover_cmd = ['ffmpeg', '-ss', str(cover_time), '-i', video_path]

        # 如果启用了GPU加速，尝试使用GPU解码
        if use_gpu and gpu_device != "auto":
            # 检查是否是NVIDIA GPU (CUDA)
            if gpu_device.startswith("cuda:"):
                # 使用NVIDIA GPU加速
                processor.logger(f"使用NVIDIA GPU加速提取封面")
                # 添加GPU解码参数
                cover_cmd.extend(['-hwaccel', 'cuda'])
                # 如果指定了具体的GPU设备，添加设备参数
                device_index = gpu_device.split(':')[1]
                if device_index.isdigit():
                    cover_cmd.extend(['-hwaccel_device', device_index])

        # 添加输出参数
        cover_cmd.extend(['-vframes', '1', '-q:v', '2', cover_path])

        # 如果设置了内存限制，添加内存限制参数
        if memory_limit > 0:
            # 转换为MB
            mem_limit_bytes = memory_limit * 1024 * 1024
            cover_cmd.insert(1, "-max_memory")
            cover_cmd.insert(2, str(mem_limit_bytes))

        cover_result = subprocess.run(cover_cmd, capture_output=True)

        # 检查封面是否成功提取
        if cover_result.returncode == 0 and os.path.exists(cover_path):
            # 如果指定了封面分辨率，调整大小
            if cover_resolution:
                try:
                    cover_img = Image.open(cover_path)
                    cover_img = cover_img.resize(cover_resolution, Image.LANCZOS)
                    cover_img.save(cover_path, quality=95)
                except Exception as e:
                    processor.logger(f"调整封面分辨率失败: {str(e)}")
            
            return {
                'success': True,
                'message': "封面提取成功"
            }
        
        # 如果ffmpeg提取封面失败，回退到moviepy方法
        processor.logger("ffmpeg提取封面失败，回退到moviepy")
        
        # 使用moviepy作为备选方案
        from moviepy.video.io.VideoFileClip import VideoFileClip

        with VideoFileClip(video_path) as video:
            # 获取视频中间帧作为封面
            cover_time = min(video.duration - 0.5, 5.0) if video.duration > 5.0 else video.duration / 2
            frame = video.get_frame(cover_time)

            # 保存封面
            cover_img = Image.fromarray(frame)

            # 如果指定了封面分辨率，调整大小
            if cover_resolution:
                cover_img = cover_img.resize(cover_resolution, Image.LANCZOS)

            cover_img.save(cover_path, quality=95)
            
            return {
                'success': True,
                'message': "封面提取成功(使用MoviePy)"
            }
            
    except Exception as e:
        return {
            'success': False,
            'message': f"提取封面失败: {str(e)}"
        }
