"""
视频格式转换模块 - 处理视频格式转换
"""

import os
import subprocess
import shutil
from typing import Dict, Any

def convert_video(processor, 
                 video_path: str, 
                 output_path: str, 
                 use_gpu: bool = False, 
                 gpu_device: str = "auto", 
                 memory_limit: int = 0) -> Dict[str, Any]:
    """
    转换视频格式
    
    Args:
        processor: 视频处理器实例
        video_path: 源视频文件路径
        output_path: 输出视频文件路径
        use_gpu: 是否使用GPU加速
        gpu_device: GPU设备
        memory_limit: 内存限制(MB)
        
    Returns:
        Dict: 转换结果，包含success、message等信息
    """
    try:
        # 使用ffmpeg转换格式
        convert_cmd = ['ffmpeg', '-i', video_path]

        # 如果启用了GPU加速，尝试使用GPU编码
        if use_gpu and gpu_device != "auto":
            # 检查是否是NVIDIA GPU (CUDA)
            if gpu_device.startswith("cuda:"):
                # 使用NVIDIA GPU加速
                processor.logger(f"使用NVIDIA GPU加速转换视频格式")
                convert_cmd.extend([
                    '-c:v', 'h264_nvenc',  # 使用NVIDIA硬件编码
                    '-preset', 'fast',     # 使用快速预设
                    '-c:a', 'copy',        # 复制音频流
                    output_path
                ])
            else:
                # 其他GPU类型，使用默认复制
                processor.logger(f"不支持的GPU类型: {gpu_device}，使用默认复制")
                convert_cmd.extend(['-c', 'copy', output_path])
        else:
            # 不使用GPU，直接复制流
            convert_cmd.extend(['-c', 'copy', output_path])

        # 如果设置了内存限制，添加内存限制参数
        if memory_limit > 0:
            # 转换为MB
            mem_limit_bytes = memory_limit * 1024 * 1024
            convert_cmd.insert(1, "-max_memory")
            convert_cmd.insert(2, str(mem_limit_bytes))

        convert_result = subprocess.run(convert_cmd, capture_output=True, text=True)

        if convert_result.returncode == 0:
            return {
                'success': True,
                'message': "视频格式转换成功"
            }
        else:
            return {
                'success': False,
                'message': f"格式转换失败: {convert_result.stderr}"
            }
    except Exception as e:
        return {
            'success': False,
            'message': f"格式转换失败: {str(e)}"
        }
