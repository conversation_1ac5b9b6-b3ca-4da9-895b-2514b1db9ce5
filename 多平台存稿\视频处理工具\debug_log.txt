正在启动网易视频预处理工具...
Python 版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
Python 路径: C:\Users\<USER>\Downloads\网易\.venv311\Scripts\python.exe
当前工作目录: C:\Users\<USER>\Downloads\网易\多平台存稿\视频处理工具
CPU 核心数: 16
总内存: 31.9 GB
可用内存: 22.2 GB
GPU 信息:
  - NVIDIA GeForce RTX 3060 Laptop GPU (6144 MiB)

=== 依赖库诊断信息 ===
警告: 系统PATH中未找到FFmpeg，正在查找本地安装...
检查路径: C:\Users\<USER>\Downloads\网易\多平台存稿\视频处理工具\网易存稿分支\ffmpeg\bin
检查路径: C:\Users\<USER>\Downloads\网易\多平台存稿\视频处理工具\ffmpeg\bin
✅ 找到本地FFmpeg: C:\Users\<USER>\Downloads\网易\多平台存稿\视频处理工具\ffmpeg\bin\ffmpeg.exe
✅ FFmpeg测试成功!
✅ 成功导入MoviePy (版本: 2.1.1)
✅ 使用导入路径: moviepy.video.io.VideoFileClip
=== 诊断信息结束 ===

正在导入UI模块...
创建主窗口...
设置DPI感知...
创建视频处理器...
创建UI实例...
检测到开发环境，使用原始窗口大小: 1800x1600，字体大小: 17
UI基类: 正在居中窗口...
屏幕尺寸: 1707x1067
系统报告的屏幕尺寸: 2560x1600
窗口初始尺寸: 1800x1600
调整后的窗口尺寸: 1800x1600
计算的居中位置: +380+0
设置窗口geometry: 1800x1600+380+0
设置后的窗口geometry: 1800x1600+380+0
窗口已居中: 1800x1600 位置: +380+0
UI基类: 窗口居中完成
✅ 调度器已启动
进度面板已创建
日志面板已创建
UI创建完成
✅ UI创建完成
UI基类: 正在居中窗口...
屏幕尺寸: 1707x1067
系统报告的屏幕尺寸: 2560x1600
窗口初始尺寸: 1800x1600
调整后的窗口尺寸: 1800x1600
计算的居中位置: +380+0
设置窗口geometry: 1800x1600+380+0
设置后的窗口geometry: 1800x1600+380+0
窗口已居中: 1800x1600 位置: +380+0
UI基类: 窗口居中完成
已切换到浅色模式
✅ 调度器已启动
欢迎使用网易视频预处理工具
✅ 界面初始化完成
✅ 配置已保存到: C:\Users\<USER>\Downloads\网易\多平台存稿\视频处理工具\preprocess_config.ini
UI实例创建成功
正在居中主窗口...
屏幕尺寸: 1707x1067
系统报告的屏幕尺寸: 2560x1600
窗口初始尺寸: 1800x1600
调整后的窗口尺寸: 1800x1600
计算的居中位置: +380+0
设置窗口geometry: 1800x1600+380+0
设置后的窗口geometry: 1800x1600+380+0
窗口已居中: 1800x1600 位置: +380+0
主窗口最终geometry: 1800x1600+380+0
窗口已创建，启动主循环...
✅ 封面分辨率已设置为 1920x1080
✅ 配置已保存到: C:\Users\<USER>\Downloads\网易\多平台存稿\视频处理工具\preprocess_config.ini
✅ 配置已保存到: C:\Users\<USER>\Downloads\网易\多平台存稿\视频处理工具\preprocess_config.ini
✅ 配置已保存到: C:\Users\<USER>\Downloads\网易\多平台存稿\视频处理工具\preprocess_config.ini
✅ 配置已保存到: C:\Users\<USER>\Downloads\网易\多平台存稿\视频处理工具\preprocess_config.ini
✅ 配置已保存到: C:\Users\<USER>\Downloads\网易\多平台存稿\视频处理工具\preprocess_config.ini
