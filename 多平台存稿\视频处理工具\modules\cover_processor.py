"""
封面处理模块 - 处理视频封面的提取和编辑
"""

import os
from typing import Dict, Tuple, Optional, Callable, Union
from PIL import Image, ImageDraw, ImageFont

from .utils import get_color_map, smart_split_filename

class CoverProcessor:
    """封面处理类，负责封面的提取和编辑"""

    def __init__(self, logger: Callable = print):
        """
        初始化封面处理器

        Args:
            logger: 日志记录函数
        """
        self.logger = logger
        self.color_map = get_color_map()

    def add_text_to_cover(self,
                         cover_path: str,
                         base_filename: str,
                         top_text: str = "",
                         bottom_text: str = "",
                         top_color: str = "#FFFFFF",
                         bottom_color: str = "#FFFFFF",
                         font_size: int = 60,
                         auto_use_filename: bool = True,
                         cover_resolution: Tuple[int, int] = None) -> bool:
        """
        添加文字到封面图片

        Args:
            cover_path: 封面图片路径
            base_filename: 基础文件名（用于自动生成文字）
            top_text: 上方文字
            bottom_text: 下方文字
            top_color: 上方文字颜色（十六进制）
            bottom_color: 下方文字颜色（十六进制）
            font_size: 字体大小
            auto_use_filename: 是否自动使用文件名
            cover_resolution: 封面分辨率，如果指定则调整大小

        Returns:
            bool: 是否成功添加文字
        """
        try:
            # 打开图片
            cover_img = Image.open(cover_path)

            # 如果指定了分辨率，调整图片大小
            if cover_resolution:
                cover_img = cover_img.resize(cover_resolution, Image.LANCZOS)

            img_width, img_height = cover_img.size

            # 准备封面文字
            final_top_text = top_text
            final_bottom_text = bottom_text

            # 根据设置和用户输入决定如何处理文字
            if auto_use_filename:
                # 预清理文件名，移除特殊字符和随机后缀
                clean_filename = base_filename

                # 如果开启了自动使用文件名功能
                if not top_text and not bottom_text:
                    # 如果用户没有填写任何文字，则智能拆分文件名为上下两部分
                    if len(clean_filename) > 0:
                        final_top_text, final_bottom_text = smart_split_filename(clean_filename)
                elif not top_text:
                    # 如果只有左上角文字为空，使用文件名前半部分
                    first_part, _ = smart_split_filename(clean_filename)
                    final_top_text = first_part
                elif not bottom_text:
                    # 如果只有右下角文字为空，使用文件名后半部分
                    if len(clean_filename) > 0:
                        _, second_part = smart_split_filename(clean_filename)
                        final_bottom_text = second_part
            elif not top_text and not bottom_text:
                # 当用户未填写左上和右下文字且未开启自动使用文件名时，智能拆分文件名
                # 预清理文件名，移除特殊字符和随机后缀
                clean_filename = base_filename
                if len(clean_filename) > 0:
                    final_top_text, final_bottom_text = smart_split_filename(clean_filename)

            # 保存原始的设定字体大小
            original_font_size = font_size

            # 绘制封面文字
            if final_top_text or final_bottom_text:
                draw = ImageDraw.Draw(cover_img)

                # 加载字体的函数
                def load_font(size):
                    try:
                        # 尝试加载微软雅黑粗体字体
                        try:
                            return ImageFont.truetype("msyhbd.ttc", size)
                        except:
                            try:
                                return ImageFont.truetype("msyh.ttc", size)
                            except:
                                try:
                                    return ImageFont.truetype("arial.ttf", size)
                                except:
                                    return ImageFont.load_default()
                    except:
                        return ImageFont.load_default()

                # 检查文字是否超出边界，如果超出，则减小字体大小
                def adjust_font_size(text, max_width, initial_size, min_size=20):
                    # 如果文本为空，直接返回
                    if not text:
                        font = load_font(initial_size)
                        return font, 0, 0, ""

                    # 初始字体大小
                    font_size = initial_size
                    font = load_font(font_size)

                    # 获取文字大小
                    bbox = draw.textbbox((0, 0), text, font=font)
                    text_width = bbox[2] - bbox[0]

                    # 如果文字宽度超过最大宽度，逐渐减小字体大小
                    while text_width > max_width and font_size > min_size:
                        font_size -= 2  # 每次减小2个点
                        font = load_font(font_size)
                        bbox = draw.textbbox((0, 0), text, font=font)
                        text_width = bbox[2] - bbox[0]

                    # 如果字体已经最小，但文本仍然太长，进行截断
                    if text_width > max_width:
                        # 智能截断，尝试保留语义
                        # 中文字符优先保留完整词组
                        has_chinese = any('\u4e00' <= char <= '\u9fff' for char in text)

                        if has_chinese:
                            # 中文文本按字符截断比较合理
                            ratio = max_width / text_width
                            keep_chars = max(int(len(text) * ratio * 0.9), 6)  # 至少保留6个字符

                            if keep_chars < len(text):
                                # 确保不会在中文字符中间截断
                                if keep_chars < len(text) - 3:
                                    text = text[:keep_chars] + "..."
                                else:
                                    # 如果只剩下几个字符，干脆全部显示
                                    pass
                        else:
                            # 英文文本尝试在空格处截断
                            words = text.split()
                            truncated = []
                            current_width = 0

                            for word in words:
                                word_bbox = draw.textbbox((0, 0), word + " ", font=font)
                                word_width = word_bbox[2] - word_bbox[0]

                                if current_width + word_width <= max_width * 0.9:
                                    truncated.append(word)
                                    current_width += word_width
                                else:
                                    break

                            if truncated and len(truncated) < len(words):
                                text = " ".join(truncated) + "..."
                            elif len(text) > 15:  # 如果是一个很长的单词
                                ratio = max_width / text_width
                                keep_chars = max(int(len(text) * ratio * 0.9), 12)
                                text = text[:keep_chars] + "..."

                        # 重新测量截断后的宽度
                        bbox = draw.textbbox((0, 0), text, font=font)
                        text_width = bbox[2] - bbox[0]

                    # 最后一次检查，极端情况处理
                    if text_width > max_width and len(text) > 10:
                        # 强制截断
                        text = text[:7] + "..."
                        bbox = draw.textbbox((0, 0), text, font=font)
                        text_width = bbox[2] - bbox[0]

                    return font, text_width, bbox[3] - bbox[1], text  # 返回字体对象、文字宽度、高度和可能被截断的文本

                # 留出边距
                margin = 20
                max_text_width = img_width - (margin * 2)

                # 绘制左上角文字
                if final_top_text:
                    text_color = self.color_map.get(top_color, (255, 255, 255))

                    # 获取最佳字体大小和文字尺寸，以及可能被截断的文本
                    top_font, text_width, text_height, truncated_top_text = adjust_font_size(final_top_text, max_text_width, original_font_size)

                    # 左上角位置
                    text_x = margin
                    text_y = margin

                    # 绘制文字阴影
                    shadow_offset = 2
                    draw.text((text_x + shadow_offset, text_y + shadow_offset),
                             truncated_top_text, font=top_font, fill=(0, 0, 0))

                    # 绘制文字
                    draw.text((text_x, text_y), truncated_top_text, font=top_font, fill=text_color)

                # 绘制右下角文字
                if final_bottom_text:
                    text_color = self.color_map.get(bottom_color, (255, 255, 255))

                    # 获取最佳字体大小和文字尺寸，以及可能被截断的文本
                    bottom_font, text_width, text_height, truncated_bottom_text = adjust_font_size(final_bottom_text, max_text_width, original_font_size)

                    # 右下角位置
                    text_x = img_width - text_width - margin
                    text_y = img_height - text_height - margin

                    # 确保文字不会超出图片边界
                    text_x = max(margin, text_x)
                    text_y = max(margin, text_y)

                    # 绘制文字阴影
                    shadow_offset = 2
                    draw.text((text_x + shadow_offset, text_y + shadow_offset),
                             truncated_bottom_text, font=bottom_font, fill=(0, 0, 0))

                    # 绘制文字
                    draw.text((text_x, text_y), truncated_bottom_text, font=bottom_font, fill=text_color)

            # 保存封面
            cover_img.save(cover_path, quality=95)
            return True
        except Exception as e:
            self.logger(f"添加封面文字失败: {str(e)}")
            return False

    def add_watermark_to_cover(self,
                              cover_path: str,
                              watermark_color: str = "#FFFFFF",
                              opacity: float = 0.1,
                              position: str = "全屏",
                              quantity: int = 1) -> bool:
        """
        为封面添加纯色水印

        Args:
            cover_path: 封面图片路径
            watermark_color: 水印颜色（十六进制）
            opacity: 水印透明度（0.0-1.0）
            position: 水印位置（"全屏", "左上角", "右上角", "左下角", "右下角", "中心"）
            quantity: 水印数量（仅在非全屏模式下有效）

        Returns:
            bool: 是否成功添加水印
        """
        try:
            # 打开封面图片
            cover_img = Image.open(cover_path)
            width, height = cover_img.size

            # 将16进制颜色转换为RGB
            r = int(watermark_color[1:3], 16)
            g = int(watermark_color[3:5], 16)
            b = int(watermark_color[5:7], 16)

            # 创建一个透明的全屏图层
            full_layer = Image.new('RGBA', (width, height), (0, 0, 0, 0))

            # 根据位置设置水印大小和位置
            if position == "全屏":
                # 全屏水印
                watermark_layer = Image.new('RGBA', (width, height), (r, g, b, int(opacity * 255)))
                full_layer = watermark_layer  # 直接使用全屏水印
            else:
                # 限制水印数量在合理范围内
                quantity = max(1, min(10, quantity))

                # 计算水印尺寸（屏幕的1/4）
                wm_width = width // 4
                wm_height = height // 4

                # 创建较小的水印
                watermark_layer = Image.new('RGBA', (wm_width, wm_height), (r, g, b, int(opacity * 255)))

                # 根据位置和数量设置水印
                if quantity == 1:
                    # 单个水印，按指定位置放置
                    if position == "左上角":
                        pos = (0, 0)
                    elif position == "右上角":
                        pos = (width - wm_width, 0)
                    elif position == "左下角":
                        pos = (0, height - wm_height)
                    elif position == "右下角":
                        pos = (width - wm_width, height - wm_height)
                    elif position == "中心":
                        pos = ((width - wm_width) // 2, (height - wm_height) // 2)

                    # 将小水印放到指定位置
                    full_layer.paste(watermark_layer, pos)
                else:
                    # 多个水印，根据数量和位置分布
                    import random

                    # 设置随机种子，确保每次生成相同的随机位置
                    random.seed(hash(cover_path) % 10000)

                    # 根据位置确定水印的分布区域
                    if position == "左上角":
                        x_range = (0, width // 2 - wm_width)
                        y_range = (0, height // 2 - wm_height)
                    elif position == "右上角":
                        x_range = (width // 2, width - wm_width)
                        y_range = (0, height // 2 - wm_height)
                    elif position == "左下角":
                        x_range = (0, width // 2 - wm_width)
                        y_range = (height // 2, height - wm_height)
                    elif position == "右下角":
                        x_range = (width // 2, width - wm_width)
                        y_range = (height // 2, height - wm_height)
                    elif position == "中心":
                        # 中心区域，水印分布在中心区域
                        center_width = width // 2
                        center_height = height // 2
                        x_range = (center_width // 2, center_width + center_width // 2 - wm_width)
                        y_range = (center_height // 2, center_height + center_height // 2 - wm_height)
                    else:
                        # 默认全图分布
                        x_range = (0, width - wm_width)
                        y_range = (0, height - wm_height)

                    # 生成不重叠的水印位置
                    positions = []
                    attempts = 0
                    max_attempts = 50  # 最大尝试次数，防止无限循环

                    while len(positions) < quantity and attempts < max_attempts:
                        # 生成随机位置
                        x = random.randint(x_range[0], max(x_range[0], x_range[1]))
                        y = random.randint(y_range[0], max(y_range[0], y_range[1]))
                        new_pos = (x, y)

                        # 检查是否与已有位置重叠
                        overlap = False
                        for pos in positions:
                            if (abs(pos[0] - x) < wm_width * 0.8 and
                                abs(pos[1] - y) < wm_height * 0.8):
                                overlap = True
                                break

                        if not overlap:
                            positions.append(new_pos)

                        attempts += 1

                    # 如果生成的位置不足，补充一些位置
                    while len(positions) < quantity:
                        x = random.randint(x_range[0], max(x_range[0], x_range[1]))
                        y = random.randint(y_range[0], max(y_range[0], y_range[1]))
                        positions.append((x, y))

                    # 将水印放到生成的位置
                    for pos in positions:
                        full_layer.paste(watermark_layer, pos)

            # 转换原图为RGBA模式
            if cover_img.mode != 'RGBA':
                cover_img = cover_img.convert('RGBA')

            # 合成图像
            result = Image.alpha_composite(cover_img, full_layer)

            # 转换回RGB并保存
            result_rgb = result.convert('RGB')
            result_rgb.save(cover_path, quality=95)

            return True
        except Exception as e:
            self.logger(f"添加封面水印失败: {str(e)}")
            return False
