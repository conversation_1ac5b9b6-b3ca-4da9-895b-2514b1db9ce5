"""
配置管理模块 - 处理配置的保存和加载
"""

import os
import tkinter as tk
from typing import List, Dict, Any, Optional

class ConfigManager:
    """配置管理类，负责保存和加载配置"""

    def __init__(self, config_file: str, tracked_vars: List[tk.Variable], logger=None, var_names: List[str] = None):
        """
        初始化配置管理器

        Args:
            config_file: 配置文件路径
            tracked_vars: 需要跟踪的变量列表
            logger: 日志记录函数，如果为None则使用print
            var_names: 变量名列表，与tracked_vars对应
        """
        self.config_file = config_file
        self.tracked_vars = tracked_vars
        self.logger = logger if logger else print
        self._auto_saving = False
        self._is_dirty = False  # 标记配置是否有更改
        self._last_values = {}  # 存储变量的上一次值
        self.var_names = var_names or []  # 变量名列表

        # 初始化变量跟踪
        self._setup_variable_tracking()

    def _get_var_name(self, var):
        """获取变量的可读名称"""
        try:
            # 找到变量在列表中的索引
            index = self.tracked_vars.index(var)
            # 如果有对应的名称，使用它
            if index < len(self.var_names):
                return self.var_names[index]
        except (ValueError, IndexError):
            pass

        # 回退到原来的方法
        return str(var).split(".")[-1]

    def _setup_variable_tracking(self):
        """设置变量跟踪，当变量值改变时标记配置为脏"""
        # 初始化存储当前值
        for var in self.tracked_vars:
            name = self._get_var_name(var)
            self._last_values[name] = var.get()

            # 添加跟踪
            var.trace_add("write", self._on_variable_change)

    def _on_variable_change(self, *args):
        """当变量值改变时的回调函数"""
        if self._auto_saving:
            return  # 如果是自动保存过程中的变化，忽略

        # 检查是否有实际变化
        for var in self.tracked_vars:
            try:
                name = str(var).split(".")[-1]

                # 安全地获取变量值，处理可能的异常
                try:
                    current_value = var.get()
                except (tk.TclError, ValueError):
                    # 如果获取值失败，根据变量类型设置默认值
                    if isinstance(var, tk.IntVar):
                        current_value = 0
                        var.set(current_value)  # 重置为有效值
                    elif isinstance(var, tk.DoubleVar):
                        current_value = 0.0
                        var.set(current_value)  # 重置为有效值
                    else:
                        # 对于其他类型，跳过此变量
                        continue

                # 检查值是否与上次保存的值不同
                if name in self._last_values and self._last_values[name] != current_value:
                    self._is_dirty = True
                    self._last_values[name] = current_value
                    break
            except Exception as e:
                # 捕获任何其他异常，确保不会中断程序
                self.logger(f"⚠️ 变量跟踪出错: {str(e)}")
                continue

    def save_config(self, show_message=True, force=False) -> bool:
        """
        保存配置到文件

        Args:
            show_message: 是否显示保存成功的消息
            force: 是否强制保存，即使配置没有变化

        Returns:
            bool: 是否成功保存
        """
        # 如果配置没有变化且不是强制保存，则跳过
        if not self._is_dirty and not force and self._auto_saving:
            return True

        try:
            config_dir = os.path.dirname(self.config_file)
            # 确保配置文件目录存在
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)

            with open(self.config_file, "w", encoding="utf-8") as f:
                for var in self.tracked_vars:
                    try:
                        # 获取变量名
                        name = self._get_var_name(var)

                        # 安全地获取变量值
                        try:
                            value = var.get()
                        except (tk.TclError, ValueError):
                            # 如果获取值失败，设置默认值
                            if isinstance(var, tk.IntVar):
                                value = 0
                                var.set(value)  # 重置为有效值
                            elif isinstance(var, tk.DoubleVar):
                                value = 0.0
                                var.set(value)  # 重置为有效值
                            elif isinstance(var, tk.StringVar):
                                value = ""
                                var.set(value)  # 重置为有效值
                            else:
                                value = ""

                        # 更新最后保存的值
                        self._last_values[name] = value

                        # 确保数值类型变量有有效值
                        if isinstance(var, tk.IntVar) and (value == "" or value is None):
                            value = 0  # 设置默认值
                        elif isinstance(var, tk.DoubleVar) and (value == "" or value is None):
                            value = 0.0  # 设置默认值

                        # 确保布尔值正确保存为字符串
                        if isinstance(var, tk.BooleanVar):
                            value = "True" if value else "False"
                    except Exception as e:
                        # 捕获任何其他异常，确保不会中断保存过程
                        self.logger(f"⚠️ 保存变量 {name} 时出错: {str(e)}")
                        continue

                    f.write(f"{name}={value}\n")

            # 重置脏标记
            self._is_dirty = False

            # 只在需要时显示消息（手动保存或明确要求）
            if show_message and not self._auto_saving:
                self.logger(f"✅ 配置已保存到: {self.config_file}")
            return True
        except Exception as e:
            self.logger(f"⚠️ 保存配置失败: {str(e)}")
            return False

    def load_config(self) -> bool:
        """
        从文件加载配置

        Returns:
            bool: 是否成功加载
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, "r", encoding="utf-8") as f:
                    config = {}
                    for line in f:
                        if "=" in line:
                            key, value = line.strip().split("=", 1)
                            config[key] = value

                # 应用配置到变量
                for var in self.tracked_vars:
                    name = self._get_var_name(var)
                    if name in config:
                        value = config[name]

                        # 根据变量类型设置值
                        if isinstance(var, tk.BooleanVar):
                            # 修复布尔值转换，确保正确处理"True"和"False"字符串
                            var.set(value.lower() in ["true", "1", "yes", "on"])
                        elif isinstance(var, tk.IntVar):
                            try:
                                var.set(int(value))
                            except ValueError:
                                # 如果转换失败，记录错误并使用默认值
                                self.logger(f"⚠️ 无法将 '{value}' 转换为整数，使用默认值")
                                pass
                        elif isinstance(var, tk.DoubleVar):
                            try:
                                # 处理空值情况
                                if value == "" or value is None:
                                    # 获取变量的默认值或使用0.0
                                    default_value = 0.0
                                    var.set(default_value)
                                else:
                                    var.set(float(value))
                            except ValueError:
                                # 如果转换失败，设置为默认值
                                self.logger(f"⚠️ 无法将 '{value}' 转换为浮点数，使用默认值")
                                var.set(0.0)
                        else:
                            var.set(value)

                self.logger("✅ 已加载保存的配置")
                return True
            return False
        except Exception as e:
            self.logger(f"⚠️ 加载配置失败: {str(e)}")
            return False

    def reset_to_defaults(self) -> bool:
        """
        重置所有配置到默认值

        Returns:
            bool: 是否成功重置
        """
        try:
            # 设置默认值
            for var in self.tracked_vars:
                if isinstance(var, tk.BooleanVar):
                    var.set(False)  # 布尔值默认为False
                elif isinstance(var, tk.IntVar):
                    var.set(0)  # 整数默认为0
                elif isinstance(var, tk.DoubleVar):
                    var.set(0.0)  # 浮点数默认为0.0
                elif isinstance(var, tk.StringVar):
                    var.set("")  # 字符串默认为空

            # 设置一些特定的默认值
            for var in self.tracked_vars:
                name = str(var).split(".")[-1]

                # 视频处理相关默认值
                if name == "video_min_duration":
                    var.set(30)
                elif name == "video_max_duration":
                    var.set(300)
                elif name == "video_ratio":
                    var.set("16:9")
                elif name == "thread_num":
                    var.set(4)
                elif name == "enable_deduplication":
                    var.set(True)

                # 封面相关默认值
                elif name == "cover_color_top" or name == "cover_color_bottom" or name == "watermark_color":
                    var.set("#FFFFFF")
                elif name == "cover_size":
                    var.set(60)
                elif name == "watermark_opacity":
                    var.set(0.1)
                elif name == "watermark_position":
                    var.set("全屏")
                elif name == "auto_use_filename":
                    var.set(True)

                # 封面分辨率默认值
                elif name == "cover_width":
                    var.set(1280)
                elif name == "cover_height":
                    var.set(720)

                # 视频格式默认值
                elif name == "output_format":
                    var.set("mp4")
                elif name == "video_codec":
                    var.set("h264")

                # 界面设置默认值
                elif name == "font_size_var":
                    # 统一使用系统默认字体大小
                    var.set(9)  # 统一使用系统默认字体大小

            self.logger("✅ 已重置所有设置为默认值")
            return True
        except Exception as e:
            self.logger(f"⚠️ 重置设置失败: {str(e)}")
            return False

    def schedule_auto_save(self, root: tk.Tk, interval: int = 60000) -> None:
        """
        计划自动保存配置

        Args:
            root: Tkinter根窗口
            interval: 自动保存间隔（毫秒）
        """
        # 使用一个内部函数来确保_auto_saving标记在函数调用期间保持一致
        def do_auto_save():
            self._auto_saving = True  # 标记为自动保存

            # 只有当配置有变化时才保存
            if self._is_dirty:
                self.save_config(show_message=False)  # 保存配置但不显示消息
                self.logger("✅ 检测到配置变化，已自动保存")

            self._auto_saving = False  # 重置标记
            # 计划下一次保存
            root.after(interval, do_auto_save)

        # 启动自动保存循环
        do_auto_save()
