"""
UI主题模块 - 定义UI主题和样式
"""

import tkinter as tk
from tkinter import ttk

# 定义主题颜色
THEME_COLORS = {
    "light": {
        "bg": "#f0f0f0",           # 系统默认浅灰色背景
        "fg": "#000000",           # 黑色文字，提高可读性
        "frame_bg": "#f0f0f0",     # 浅灰色框架背景
        "entry_bg": "#ffffff",     # 输入框背景保持白色
        "entry_fg": "#000000",     # 输入框文字颜色
        "button_bg": "#e1e1e1",    # 浅灰色按钮背景
        "button_hover": "#d4d4d4", # 按钮悬停颜色
        "button_active": "#c7c7c7", # 按钮激活颜色
        "highlight_bg": "#4dabf7", # 蓝色高亮
        "highlight_fg": "#ffffff", # 高亮文字颜色
        "canvas_bg": "#ffffff",    # 画布背景
        "log_bg": "#ffffff",       # 日志背景
        "log_fg": "#000000",       # 日志文字颜色
        "border": "#d0d0d0",       # 边框颜色
        "success": "#008000",      # 成功色 (绿色)
        "warning": "#ffa500",      # 警告色 (橙色)
        "error": "#ff0000",        # 错误色 (红色)
        "info": "#0000ff"          # 信息色 (蓝色)
    },
    "dark": {
        "bg": "#121212",           # 深灰色背景，比纯黑更护眼
        "fg": "#e9ecef",           # 浅灰色文字，提高可读性
        "frame_bg": "#1e1e1e",     # 稍亮的框架背景，增加层次感
        "entry_bg": "#2d2d2d",     # 更深的灰色输入框背景
        "entry_fg": "#e9ecef",     # 输入框文字颜色
        "button_bg": "#2d2d2d",    # 按钮背景
        "button_hover": "#3d3d3d", # 按钮悬停颜色
        "button_active": "#4d4d4d", # 按钮激活颜色
        "highlight_bg": "#228be6", # 更鲜明的蓝色高亮
        "highlight_fg": "#ffffff", # 高亮文字颜色
        "canvas_bg": "#2d2d2d",    # 画布背景
        "log_bg": "#1e1e1e",       # 日志背景
        "log_fg": "#e9ecef",       # 日志文字颜色
        "border": "#3d3d3d",       # 边框颜色
        "success": "#40c057",      # 成功色
        "warning": "#fab005",      # 警告色
        "error": "#fa5252",        # 错误色
        "info": "#15aabf"          # 信息色
    }
}

def configure_fonts(ui_instance):
    """配置字体"""
    # 创建字体 - 使用更粗的字体
    import tkinter.font as tkfont

    # 获取系统字体
    available_fonts = list(tkfont.families())
    preferred_fonts = ["Microsoft YaHei UI", "微软雅黑", "SimHei", "黑体", "Arial", "Segoe UI", "Tahoma"]

    # 选择一个可用的粗体字体
    font_family = "TkDefaultFont"  # 默认备选
    for preferred_font in preferred_fonts:
        if preferred_font in available_fonts:
            font_family = preferred_font
            break

    # 创建字体，统一使用系统默认字体大小
    system_font_size = 9  # 统一使用系统默认字体大小
    ui_instance.default_font = (font_family, system_font_size, "normal")
    ui_instance.bold_font = (font_family, system_font_size, "bold")
    ui_instance.large_font = (font_family, system_font_size + 2, "normal")
    ui_instance.large_bold_font = (font_family, system_font_size + 2, "bold")
    ui_instance.small_font = (font_family, system_font_size - 1, "normal")

    # 设置默认字体 - 统一使用系统默认字体大小
    system_font_size = 9  # 统一使用系统默认字体大小
    default_font = tkfont.nametofont("TkDefaultFont")
    default_font.configure(family=font_family, size=system_font_size, weight="normal")

    # 设置文本字体 - 统一使用系统默认字体大小
    text_font = tkfont.nametofont("TkTextFont")
    text_font.configure(family=font_family, size=system_font_size, weight="normal")

    # 设置固定宽度字体 - 统一使用系统默认字体大小
    fixed_font = tkfont.nametofont("TkFixedFont")
    fixed_font.configure(family=font_family, size=system_font_size, weight="normal")

    # 更新字体大小标签
    if hasattr(ui_instance, 'font_size_label'):
        ui_instance.font_size_label.config(text=f"当前大小: {system_font_size}px", font=ui_instance.bold_font)

def increase_font_size(ui_instance):
    """增大字体大小"""
    if ui_instance.default_font_size < 24:
        ui_instance.default_font_size += 1
        ui_instance.font_size_var.set(ui_instance.default_font_size)
        # 直接调用_update_font_size函数，它会保存配置
        _update_font_size(ui_instance)
        # 记录日志
        ui_instance.log(f"字体大小已增加到 {ui_instance.default_font_size}")

def decrease_font_size(ui_instance):
    """减小字体大小"""
    if ui_instance.default_font_size > 8:
        ui_instance.default_font_size -= 1
        ui_instance.font_size_var.set(ui_instance.default_font_size)
        # 直接调用_update_font_size函数，它会保存配置
        _update_font_size(ui_instance)
        # 记录日志
        ui_instance.log(f"字体大小已减小到 {ui_instance.default_font_size}")

def _update_font_size(ui_instance):
    """更新字体大小"""
    # 统一使用系统默认字体大小
    system_font_size = 9  # 统一使用系统默认字体大小
    ui_instance.default_font_size = system_font_size
    ui_instance.font_size_var.set(system_font_size)

    # 应用字体设置
    ui_instance.configure_fonts()
    ui_instance.apply_theme()

    # 更新日志文本字体 - 统一使用系统默认字体大小
    if hasattr(ui_instance, 'log_text'):
        # 创建新的日志字体
        import tkinter.font as tkfont
        system_font_size = 9  # 统一使用系统默认字体大小
        log_font = tkfont.Font(family="Courier New", size=system_font_size)
        ui_instance.log_text.config(font=log_font)

        # 更新日志文本标签字体
        if hasattr(ui_instance.log_text, 'tag_configure'):
            # 创建粗体版本用于标题和重要信息
            bold_log_font = tkfont.Font(family="Courier New", size=system_font_size, weight="bold")
            ui_instance.log_text.tag_configure("section", font=bold_log_font)

    # 更新所有预览
    ui_instance._update_all_previews()

    # 保存配置
    ui_instance.config_manager.save_config(show_message=False)

def toggle_dark_mode(ui_instance):
    """切换黑暗模式"""
    # 应用主题
    ui_instance.apply_theme()

    # 保存配置
    ui_instance.config_manager.save_config(show_message=False)

def apply_theme(ui_instance):
    """应用当前主题 - 现代化设计风格"""
    # 获取当前主题
    theme_key = "dark" if ui_instance.enable_dark_mode.get() else "light"
    theme = THEME_COLORS[theme_key]

    # 创建或获取ttk样式
    style = ttk.Style()

    # 尝试设置主题基础
    try:
        if theme_key == "dark":
            style.theme_use("clam")  # 使用clam主题作为黑暗模式的基础
        else:
            style.theme_use("default")  # 使用默认主题作为浅色模式的基础
    except:
        pass  # 如果主题不可用，忽略错误

    # 配置基本样式 - 现代化设计，使用更粗的字体
    style.configure("TFrame", background=theme["bg"])
    style.configure("TLabel",
                   background=theme["bg"],
                   foreground=theme["fg"],
                   font=ui_instance.default_font)

    # 标签框架样式 - 添加圆角和边框效果
    style.configure("TLabelframe",
                   background=theme["bg"],
                   foreground=theme["fg"],
                   borderwidth=1,
                   relief="solid",
                   bordercolor=theme["border"])
    style.configure("TLabelframe.Label",
                   background=theme["bg"],
                   foreground=theme["fg"],
                   font=ui_instance.bold_font)

    # 确保标签框架标题文字可见
    style.map("TLabelframe.Label",
             background=[("active", theme["bg"])],
             foreground=[("active", theme["fg"])])

    # 按钮样式 - 现代风格按钮，添加圆角和悬停效果，增大尺寸
    style.configure("TButton",
                   background=theme["button_bg"],
                   foreground=theme["fg"],
                   borderwidth=1,
                   relief="flat",
                   padding=(15, 8),  # 增大按钮内边距
                   font=ui_instance.bold_font)
    style.map("TButton",
             background=[("active", theme["button_hover"]),
                        ("pressed", theme["button_active"])],
             foreground=[("active", theme["fg"]),
                        ("pressed", theme["fg"])])

    # 复选框和单选按钮 - 现代风格，增大字体
    style.configure("TCheckbutton",
                   background=theme["bg"],
                   foreground=theme["fg"],
                   font=ui_instance.default_font)
    style.map("TCheckbutton",
             background=[("active", theme["bg"])],
             foreground=[("active", theme["fg"])])

    style.configure("TRadiobutton",
                   background=theme["bg"],
                   foreground=theme["fg"],
                   font=ui_instance.default_font)
    style.map("TRadiobutton",
             background=[("active", theme["bg"])],
             foreground=[("active", theme["fg"])])

    # 下拉框和输入框 - 现代风格，添加圆角，增大尺寸
    style.configure("TCombobox",
                   fieldbackground=theme["entry_bg"],
                   foreground=theme["entry_fg"],
                   selectbackground=theme["highlight_bg"],
                   selectforeground=theme["highlight_fg"],
                   borderwidth=1,
                   padding=8,  # 增大内边距
                   font=ui_instance.default_font)
    style.map("TCombobox",
             fieldbackground=[("readonly", theme["entry_bg"])],
             selectbackground=[("readonly", theme["highlight_bg"])],
             selectforeground=[("readonly", theme["highlight_fg"])])

    style.configure("TSpinbox",
                   fieldbackground=theme["entry_bg"],
                   foreground=theme["entry_fg"],
                   selectbackground=theme["highlight_bg"],
                   selectforeground=theme["highlight_fg"],
                   borderwidth=1,
                   arrowcolor=theme["fg"],
                   padding=8,  # 增大内边距
                   font=ui_instance.default_font)

    style.configure("TEntry",
                   fieldbackground=theme["entry_bg"],
                   foreground=theme["entry_fg"],
                   selectbackground=theme["highlight_bg"],
                   selectforeground=theme["highlight_fg"],
                   borderwidth=1,
                   padding=8,  # 增大内边距
                   font=ui_instance.default_font)

    # 配置特殊样式 - 更明显的开始按钮，添加渐变效果
    style.configure("Start.TButton",
                   background=theme["highlight_bg"],
                   foreground=theme["highlight_fg"],
                   font=ui_instance.large_bold_font,
                   borderwidth=1,
                   relief="flat",
                   padding=(15, 8))
    style.map("Start.TButton",
             background=[("active", theme["highlight_bg"]),
                        ("pressed", theme["highlight_bg"])],
             foreground=[("active", theme["highlight_fg"]),
                        ("pressed", theme["highlight_fg"])])

    style.configure("Stop.TButton",
                   background=theme["button_bg"],
                   foreground=theme["fg"],
                   font=ui_instance.large_font,
                   borderwidth=1,
                   relief="flat",
                   padding=(15, 8))
    style.map("Stop.TButton",
             background=[("active", theme["button_hover"]),
                        ("pressed", theme["button_active"])],
             foreground=[("active", theme["fg"]),
                        ("pressed", theme["fg"])])

    # 配置统计标签样式，使用主题定义的颜色
    style.configure("Success.TLabel",
                   foreground=theme["success"],
                   background=theme["bg"],
                   font=ui_instance.bold_font)
    style.configure("Fail.TLabel",
                   foreground=theme["error"],
                   background=theme["bg"],
                   font=ui_instance.bold_font)
    style.configure("Warning.TLabel",
                   foreground=theme["warning"],
                   background=theme["bg"],
                   font=ui_instance.default_font)
    style.configure("Info.TLabel",
                   foreground=theme["info"],
                   background=theme["bg"],
                   font=ui_instance.default_font)

    # 设置根窗口背景
    ui_instance.root.configure(background=theme["bg"])

    # 设置日志文本区域颜色 - 添加内边距和圆角效果
    if hasattr(ui_instance, 'log_text'):
        ui_instance.log_text.configure(
            bg=theme["log_bg"],
            fg=theme["log_fg"],
            insertbackground=theme["fg"],
            selectbackground=theme["highlight_bg"],
            selectforeground=theme["highlight_fg"],
            relief="flat",
            borderwidth=1,
            padx=5,
            pady=5
        )

    # 设置预览画布背景
    if hasattr(ui_instance, 'font_preview_canvas'):
        ui_instance.font_preview_canvas.configure(bg=theme["canvas_bg"])
        ui_instance._update_font_size_preview()  # 刷新预览

    # 更新所有子组件的颜色
    _update_all_widgets_theme(ui_instance, ui_instance.root, theme)

    # 记录日志
    if theme_key == "dark":
        ui_instance.log("已切换到黑暗模式 (深灰背景和柔和文字，更护眼)")
    else:
        ui_instance.log("已切换到浅色模式")

def _update_all_widgets_theme(ui_instance, parent, theme):
    """递归更新所有子组件的主题颜色 - 现代化设计风格"""
    try:
        for child in parent.winfo_children():
            # 递归处理子组件
            _update_all_widgets_theme(ui_instance, child, theme)

            # 根据组件类型应用不同的样式
            try:
                widget_class = child.winfo_class()

                # 处理标准Tkinter组件
                if widget_class == 'Frame':
                    child.configure(
                        bg=theme["bg"],
                        highlightbackground=theme["border"],
                        highlightthickness=0
                    )
                elif widget_class == 'Label':
                    child.configure(
                        bg=theme["bg"],
                        fg=theme["fg"]
                    )
                elif widget_class == 'Button':
                    child.configure(
                        bg=theme["button_bg"],
                        fg=theme["fg"],
                        activebackground=theme["button_hover"],
                        activeforeground=theme["fg"],
                        relief="flat",
                        borderwidth=1,
                        padx=10,
                        pady=5
                    )
                elif widget_class == 'Entry':
                    child.configure(
                        bg=theme["entry_bg"],
                        fg=theme["entry_fg"],
                        insertbackground=theme["fg"],  # 光标颜色
                        selectbackground=theme["highlight_bg"],
                        selectforeground=theme["highlight_fg"],
                        relief="flat",
                        borderwidth=1,
                        highlightbackground=theme["border"],
                        highlightthickness=1,
                        highlightcolor=theme["highlight_bg"]
                    )
                elif widget_class == 'Text':
                    child.configure(
                        bg=theme["log_bg"],
                        fg=theme["log_fg"],
                        insertbackground=theme["fg"],  # 光标颜色
                        selectbackground=theme["highlight_bg"],
                        selectforeground=theme["highlight_fg"],
                        relief="flat",
                        borderwidth=1,
                        highlightbackground=theme["border"],
                        highlightthickness=1,
                        highlightcolor=theme["highlight_bg"],
                        padx=5,
                        pady=5
                    )
                elif widget_class == 'Canvas':
                    # 只更新非预览画布
                    if hasattr(ui_instance, 'font_preview_canvas') and child != ui_instance.font_preview_canvas:
                        # 检查是否是颜色预览画布
                        is_color_preview = False
                        for attr_name in ['color_preview_top', 'color_preview_bottom']:
                            if hasattr(ui_instance, attr_name) and child == getattr(ui_instance, attr_name):
                                is_color_preview = True
                                break

                        if not is_color_preview:
                            child.configure(
                                bg=theme["canvas_bg"],
                                highlightbackground=theme["border"],
                                highlightthickness=1
                            )

                # 处理Tkinter菜单
                elif widget_class == 'Menu':
                    child.configure(
                        bg=theme["bg"],
                        fg=theme["fg"],
                        activebackground=theme["button_hover"],
                        activeforeground=theme["fg"],
                        relief="flat",
                        borderwidth=0
                    )

                # 处理下拉菜单和组合框
                elif widget_class == 'TCombobox':
                    # 尝试设置下拉列表颜色
                    try:
                        child.tk.eval(f'[ttk::combobox::PopdownWindow {child}].f.l configure -background {theme["entry_bg"]} -foreground {theme["entry_fg"]} -selectbackground {theme["highlight_bg"]} -selectforeground {theme["highlight_fg"]}')
                    except Exception:
                        # 忽略错误，可能是因为下拉列表尚未创建
                        pass

                # 处理LabelFrame标题
                elif widget_class == 'TLabelframe':
                    # 尝试找到并更新标题标签
                    for sub_child in child.winfo_children():
                        if sub_child.winfo_class() == 'TLabel':
                            sub_child.configure(
                                background=theme["bg"],
                                foreground=theme["fg"],
                                font=ui_instance.bold_font
                            )
            except Exception:
                # 忽略无法配置的组件
                pass
    except Exception:
        # 忽略无法获取子组件的错误
        pass
