#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置管理器 - 负责设置和配置相关功能
遵循MECE原则：与设置配置相关的所有功能集中管理
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from typing import Dict, Any, Optional

# logger导入已移除，使用parent_ui.log代替
from 网易号存稿.ui.styles.theme import Theme


class SettingsManager:
    """设置管理器 - 负责所有设置配置相关功能"""
    
    def __init__(self, parent_ui, config_manager):
        """
        初始化设置管理器
        
        Args:
            parent_ui: 主UI实例
            config_manager: 配置管理器
        """
        self.parent_ui = parent_ui
        self.config_manager = config_manager
        
        # 设置变量
        self._init_setting_variables()

        # 不再绑定变量变化事件，因为主UI已经绑定了
        # self.bind_variable_changes()  # 移除重复绑定
    
    def _init_setting_variables(self):
        """初始化设置变量"""
        # 目录设置 - 使用主UI中的变量，避免重复绑定
        self.account_dir = self.parent_ui.account_dir
        self.video_dir = self.parent_ui.video_dir
        self.cover_dir = self.parent_ui.cover_dir
        self.processed_dir = self.parent_ui.processed_dir
        self.processed_covers_dir = self.parent_ui.processed_covers_dir
        self.violation_dir = self.parent_ui.violation_dir
        self.screenshots_dir = self.parent_ui.screenshots_dir

        # 系统运行设置已移至存稿设置中，此处不再重复定义

        # 界面设置 - 使用主UI中的变量
        self.dark_mode = self.parent_ui.dark_mode
        self.window_width = self.parent_ui.window_width
        self.window_height = self.parent_ui.window_height

        # 加载配置
        self._load_settings()
    
    def _load_settings(self):
        """加载设置"""
        try:
            current_platform = getattr(self.parent_ui, 'current_platform', 'netease')

            # 目录设置 - 加载所有目录配置
            self.account_dir.set(self.config_manager.get("account_dir", "", platform=current_platform))
            self.video_dir.set(self.config_manager.get("video_dir", "", platform=current_platform))
            self.cover_dir.set(self.config_manager.get("cover_dir", "", platform=current_platform))
            self.processed_dir.set(self.config_manager.get("processed_dir", "", platform=current_platform))
            self.processed_covers_dir.set(self.config_manager.get("processed_covers_dir", "", platform=current_platform))
            self.violation_dir.set(self.config_manager.get("violation_dir", "", platform=current_platform))
            self.screenshots_dir.set(self.config_manager.get("screenshots_dir", "", platform=current_platform))
            
            # 系统运行设置已移至存稿设置中，此处不再加载
            
            # 界面设置
            self.dark_mode.set(self.config_manager.get("dark_mode", False))
            self.window_width.set(self.config_manager.get("window_width", 1400))
            self.window_height.set(self.config_manager.get("window_height", 900))
            
        except Exception as e:
            self.parent_ui.log(f"❌ 加载设置失败: {e}")
    
    def create_settings_tab(self, parent):
        """创建现代化设置标签页"""
        # 创建主容器
        main_container = ttk.Frame(parent)
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 创建设置内容
        self._create_settings_content(main_container)
        
        return main_container

    def create_modern_dir_settings(self, parent):
        """创建现代化目录设置"""
        # 创建主容器
        main_container = ttk.Frame(parent)
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 创建目录设置
        self._create_directory_settings(main_container)

        # 创建保存按钮
        self._create_save_button(main_container)

        return main_container

    def _create_settings_content(self, parent):
        """创建设置内容"""
        # 创建滚动框架（删除滚动条）
        canvas = tk.Canvas(parent)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")

        # 目录设置
        self._create_directory_settings(scrollable_frame)

        # 界面设置
        self._create_interface_settings(scrollable_frame)

        # 保存按钮
        self._create_save_button(scrollable_frame)

        # 布局（删除滚动条）
        canvas.pack(fill="both", expand=True)

        # 绑定鼠标滚轮事件以支持滚动
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind('<Enter>', _bind_mousewheel)
        canvas.bind('<Leave>', _unbind_mousewheel)
    
    def _create_directory_settings(self, parent):
        """创建目录设置"""
        dir_frame = ttk.LabelFrame(parent, text="📁 目录设置", padding=15)
        dir_frame.pack(fill=tk.X, pady=(0, 15))

        # 创建目录设置项的辅助函数
        def create_dir_setting(parent_frame, label_text, var, is_last=False):
            frame = ttk.Frame(parent_frame)
            frame.pack(fill=tk.X, pady=(0, 0 if is_last else 10))

            ttk.Label(frame, text=label_text, width=15).pack(side=tk.LEFT)
            ttk.Entry(frame, textvariable=var, width=50).pack(side=tk.LEFT, padx=(10, 5), fill=tk.X, expand=True)
            ttk.Button(frame, text="浏览", command=lambda: self.select_dir(var)).pack(side=tk.RIGHT)

        # 创建所有目录设置
        create_dir_setting(dir_frame, "账号目录:", self.account_dir)
        create_dir_setting(dir_frame, "视频目录:", self.video_dir)
        create_dir_setting(dir_frame, "封面目录:", self.cover_dir)
        create_dir_setting(dir_frame, "已处理目录:", self.processed_dir)
        create_dir_setting(dir_frame, "已处理封面:", self.processed_covers_dir)
        create_dir_setting(dir_frame, "违规目录:", self.violation_dir)
        create_dir_setting(dir_frame, "截图目录:", self.screenshots_dir, is_last=True)




    def _create_interface_settings(self, parent):
        """创建界面设置"""
        interface_frame = ttk.LabelFrame(parent, text="🎨 界面设置", padding=15)
        interface_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 主题设置
        theme_frame = ttk.Frame(interface_frame)
        theme_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Checkbutton(
            theme_frame,
            text="深色模式",
            variable=self.dark_mode,
            command=self.apply_theme
        ).pack(side=tk.LEFT)
        
        # 窗口大小设置
        size_frame = ttk.Frame(interface_frame)
        size_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(size_frame, text="窗口大小:").pack(side=tk.LEFT)
        
        ttk.Label(size_frame, text="宽度").pack(side=tk.LEFT, padx=(20, 5))
        ttk.Spinbox(
            size_frame,
            from_=800,
            to=2000,
            textvariable=self.window_width,
            width=10
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(size_frame, text="高度").pack(side=tk.LEFT, padx=(10, 5))
        ttk.Spinbox(
            size_frame,
            from_=600,
            to=1500,
            textvariable=self.window_height,
            width=10
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(size_frame, text="应用", command=self.set_window_size).pack(side=tk.LEFT, padx=(10, 0))
        
        # 预设大小按钮
        preset_frame = ttk.Frame(interface_frame)
        preset_frame.pack(fill=tk.X)
        
        ttk.Label(preset_frame, text="预设大小:").pack(side=tk.LEFT)
        ttk.Button(preset_frame, text="1200x800", command=lambda: self.set_preset_size(1200, 800)).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Button(preset_frame, text="1400x900", command=lambda: self.set_preset_size(1400, 900)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(preset_frame, text="1600x1000", command=lambda: self.set_preset_size(1600, 1000)).pack(side=tk.LEFT, padx=(0, 5))
    
    def _create_save_button(self, parent):
        """创建保存按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(
            button_frame,
            text="💾 保存设置",
            command=lambda: self.save_config(silent=False)
        ).pack(side=tk.RIGHT)
    
    def select_dir(self, var):
        """选择目录"""
        directory = filedialog.askdirectory(
            title="选择目录",
            initialdir=var.get()
        )
        if directory:
            var.set(directory)
            # 立即保存目录设置，避免丢失
            self._save_directory_setting(var)

    def _save_directory_setting(self, var):
        """立即保存单个目录设置"""
        try:
            current_platform = getattr(self.parent_ui, 'current_platform', 'netease')

            # 确定是哪个目录变量
            if var == self.account_dir:
                self.config_manager.set("account_dir", var.get(), platform=current_platform)
            elif var == self.video_dir:
                self.config_manager.set("video_dir", var.get(), platform=current_platform)
            elif var == self.cover_dir:
                self.config_manager.set("cover_dir", var.get(), platform=current_platform)
            elif var == self.processed_dir:
                self.config_manager.set("processed_dir", var.get(), platform=current_platform)
            elif var == self.processed_covers_dir:
                self.config_manager.set("processed_covers_dir", var.get(), platform=current_platform)
            elif var == self.violation_dir:
                self.config_manager.set("violation_dir", var.get(), platform=current_platform)
            elif var == self.screenshots_dir:
                self.config_manager.set("screenshots_dir", var.get(), platform=current_platform)

            # 强制立即保存配置
            self.config_manager.save_config(force=True)

            # 记录日志
            if hasattr(self.parent_ui, 'log'):
                self.parent_ui.log(f"目录设置已保存: {var.get()}", "INFO")

        except Exception as e:
            if hasattr(self.parent_ui, 'log'):
                self.parent_ui.log(f"保存目录设置失败: {e}", "ERROR")

    def save_config(self, silent=True):
        """保存配置

        Args:
            silent: 是否静默保存（不显示弹窗），默认为True
        """
        try:
            current_platform = getattr(self.parent_ui, 'current_platform', 'netease')

            # 保存所有目录设置
            self.config_manager.set("account_dir", self.account_dir.get(), platform=current_platform)
            self.config_manager.set("video_dir", self.video_dir.get(), platform=current_platform)
            self.config_manager.set("cover_dir", self.cover_dir.get(), platform=current_platform)
            self.config_manager.set("processed_dir", self.processed_dir.get(), platform=current_platform)
            self.config_manager.set("processed_covers_dir", self.processed_covers_dir.get(), platform=current_platform)
            self.config_manager.set("violation_dir", self.violation_dir.get(), platform=current_platform)
            self.config_manager.set("screenshots_dir", self.screenshots_dir.get(), platform=current_platform)

            # 系统运行设置已移至存稿设置中，此处不再保存

            # 保存界面设置
            self.config_manager.set("dark_mode", self.dark_mode.get())
            self.config_manager.set("window_width", self.window_width.get())
            self.config_manager.set("window_height", self.window_height.get())

            # 保存配置文件
            result = self.config_manager.save_config()

            # 更新相关组件
            self._update_related_components()

            if result:
                if not silent:
                    self.parent_ui.log("✅ 设置已保存")
                    messagebox.showinfo("成功", "设置已保存")
                else:
                    self.parent_ui.log("✅ 设置已静默保存")
                return True
            else:
                error_msg = "配置文件保存失败"
                self.parent_ui.log(f"❌ {error_msg}")
                if not silent:
                    messagebox.showerror("错误", error_msg)
                return False

        except Exception as e:
            error_msg = f"保存设置失败: {str(e)}"
            self.parent_ui.log(f"❌ {error_msg}")
            if not silent:
                messagebox.showerror("错误", error_msg)
            return False
    
    def _update_related_components(self):
        """更新相关组件"""
        try:
            # 更新账号管理器目录
            if hasattr(self.parent_ui, 'account_manager'):
                self.parent_ui.account_manager.account_dir = self.account_dir.get()
                # 只有当目录真正改变时才重新加载账号列表
                if self.parent_ui.account_manager.account_dir != self.account_dir.get():
                    self.parent_ui.account_manager.load_accounts()

            # 同步更新主UI中的目录变量（不重新加载配置）
            if hasattr(self.parent_ui, 'account_dir'):
                self.parent_ui.account_dir.set(self.account_dir.get())
            if hasattr(self.parent_ui, 'video_dir'):
                self.parent_ui.video_dir.set(self.video_dir.get())
            if hasattr(self.parent_ui, 'cover_dir'):
                self.parent_ui.cover_dir.set(self.cover_dir.get())
            if hasattr(self.parent_ui, 'processed_dir'):
                self.parent_ui.processed_dir.set(self.processed_dir.get())
            if hasattr(self.parent_ui, 'processed_covers_dir'):
                self.parent_ui.processed_covers_dir.set(self.processed_covers_dir.get())
            if hasattr(self.parent_ui, 'violation_dir'):
                self.parent_ui.violation_dir.set(self.violation_dir.get())
            if hasattr(self.parent_ui, 'screenshots_dir'):
                self.parent_ui.screenshots_dir.set(self.screenshots_dir.get())

            # 确保目录存在（静默模式，避免重复日志）
            if hasattr(self.parent_ui, 'create_required_dirs'):
                self.parent_ui.create_required_dirs(silent=True)

            # 刷新账号表格显示
            if hasattr(self.parent_ui, 'accounts_table') and self.parent_ui.accounts_table:
                # 获取当前账号列表并刷新显示
                if hasattr(self.parent_ui, 'account_manager') and self.parent_ui.account_manager:
                    accounts = self.parent_ui.account_manager.accounts
                    self.parent_ui.accounts_table.refresh_accounts(accounts)
                else:
                    # 如果账号管理器不存在，传递空列表
                    self.parent_ui.accounts_table.refresh_accounts([])

        except Exception as e:
            self.parent_ui.log(f"❌ 更新相关组件失败: {e}")
    
    def apply_theme(self):
        """应用主题设置"""
        try:
            theme_mode = "dark" if self.dark_mode.get() else "light"
            Theme.apply(self.parent_ui.root, theme_mode)

            # 更新滚动条样式
            for widget in self.parent_ui.root.winfo_children():
                if isinstance(widget, tk.Scrollbar):
                    Theme.apply_scrollbar_style(widget, theme_mode)

            # 移除重复的日志输出，由主UI负责日志
            # self.parent_ui.log(f"✅ 已切换到{'深色' if self.dark_mode.get() else '浅色'}主题")

        except Exception as e:
            self.parent_ui.log(f"❌ 应用主题失败: {e}")
    
    def set_window_size(self):
        """设置窗口大小"""
        try:
            width = self.window_width.get()
            height = self.window_height.get()

            # 验证尺寸
            if width < 800 or height < 600:
                messagebox.showwarning("警告", "窗口尺寸过小，最小为800x600")
                return

            # 设置窗口大小
            self.parent_ui.root.geometry(f"{width}x{height}")

            # 只有在非平台切换状态时才居中窗口
            # 避免平台切换时自动居中，保持用户的窗口位置
            if (hasattr(self.parent_ui, '_center_window') and
                not getattr(self.parent_ui, '_platform_switching', False)):
                self.parent_ui._center_window(self.parent_ui.root)

            # 移除重复的日志输出，由主UI负责日志
            # self.parent_ui.log(f"✅ 窗口大小已设置为 {width}x{height}")

        except Exception as e:
            self.parent_ui.log(f"❌ 设置窗口大小失败: {e}")
    
    def set_preset_size(self, width, height):
        """设置预设窗口大小"""
        self.window_width.set(width)
        self.window_height.set(height)
        self.set_window_size()
    
    def toggle_theme(self):
        """切换主题"""
        self.dark_mode.set(not self.dark_mode.get())
        self.apply_theme()
    
    def bind_variable_changes(self):
        """绑定变量变化事件"""
        try:
            # 绑定所有目录变量变化事件
            self.account_dir.trace_add("write", lambda *_: self.save_config())
            self.video_dir.trace_add("write", lambda *_: self.save_config())
            self.cover_dir.trace_add("write", lambda *_: self.save_config())
            self.processed_dir.trace_add("write", lambda *_: self.save_config())
            self.processed_covers_dir.trace_add("write", lambda *_: self.save_config())
            self.violation_dir.trace_add("write", lambda *_: self.save_config())
            self.screenshots_dir.trace_add("write", lambda *_: self.save_config())

            # 系统运行设置已移至存稿设置中，此处不再绑定

        except Exception as e:
            self.parent_ui.log(f"❌ 绑定变量变化事件失败: {e}")
    
    def update_platform_settings(self, platform):
        """更新平台设置"""
        try:
            # 重新加载当前平台的设置
            self._load_settings()
            
            self.parent_ui.log(f"✅ 已切换到 {platform} 平台设置")
            
        except Exception as e:
            self.parent_ui.log(f"❌ 更新平台设置失败: {e}")
    
    def show_draft_settings(self):
        """显示存稿设置对话框"""
        try:
            from 网易号存稿.ui.dialogs.draft_settings_dialog import DraftSettingsDialog

            # 记录设置前的值
            old_settings = {
                "draft_limit": self.parent_ui.draft_limit.get(),
                "loop_limit": self.parent_ui.loop_limit.get(),
                "concurrent_accounts": self.parent_ui.concurrent_accounts.get(),
                "random_video_allocation": self.parent_ui.random_video_allocation.get(),
                "headless_mode": self.parent_ui.headless_mode.get(),
                "archive_completed": self.parent_ui.archive_completed.get(),
                "query_headless_mode": self.parent_ui.query_headless_mode.get(),
                "query_max_threads": self.parent_ui.query_max_threads.get()
            }

            self.parent_ui.log("打开存稿设置对话框")

            # 创建存稿设置对话框
            settings_dialog = DraftSettingsDialog(
                self.parent_ui.root,
                self.parent_ui.draft_limit,
                self.parent_ui.loop_limit,
                self.parent_ui.concurrent_accounts,
                self.parent_ui.random_video_allocation,
                self.parent_ui.headless_mode,
                self.parent_ui.archive_completed,
                self.parent_ui.query_headless_mode,
                self.parent_ui.query_max_threads,
                self.parent_ui.auto_close,
                self.parent_ui.concurrent_mode,
                self.parent_ui.max_workers,
                self.config_manager,
                self.parent_ui.current_platform
            )

            # 等待对话框关闭
            self.parent_ui.root.wait_window(settings_dialog.dialog)

            # 记录设置后的值
            new_settings = {
                "draft_limit": self.parent_ui.draft_limit.get(),
                "loop_limit": self.parent_ui.loop_limit.get(),
                "concurrent_accounts": self.parent_ui.concurrent_accounts.get(),
                "random_video_allocation": self.parent_ui.random_video_allocation.get(),
                "headless_mode": self.parent_ui.headless_mode.get(),
                "archive_completed": self.parent_ui.archive_completed.get(),
                "query_headless_mode": self.parent_ui.query_headless_mode.get(),
                "query_max_threads": self.parent_ui.query_max_threads.get()
            }

            # 检查是否有设置变化
            has_changes = any(old_settings[key] != new_settings[key] for key in old_settings)

            # 无论是否有变化，都强制保存配置
            self._save_draft_settings()

        except Exception as e:
            self.parent_ui.log(f"❌ 显示存稿设置对话框失败: {e}")
            messagebox.showerror("错误", f"显示存稿设置对话框失败: {e}")

    def _save_draft_settings(self):
        """保存存稿设置"""
        try:
            import os
            import json

            # 确保配置目录存在
            config_file = self.config_manager.config_file
            config_dir = os.path.dirname(config_file)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)

            # 获取当前平台
            current_platform = getattr(self.parent_ui, 'current_platform', 'netease')

            # 先保存存稿设置到配置管理器
            try:
                self.config_manager.set("draft_limit", self.parent_ui.draft_limit.get(), platform=current_platform)
                self.config_manager.set("loop_limit", self.parent_ui.loop_limit.get(), platform=current_platform)
                self.config_manager.set("concurrent_accounts", self.parent_ui.concurrent_accounts.get(), platform=current_platform)
                self.config_manager.set("random_video_allocation", self.parent_ui.random_video_allocation.get(), platform=current_platform)
                self.config_manager.set("headless_mode", self.parent_ui.headless_mode.get(), platform=current_platform)
                self.config_manager.set("archive_completed", self.parent_ui.archive_completed.get(), platform=current_platform)

                # 如果有查询设置，也保存
                if hasattr(self.parent_ui, 'query_headless_mode'):
                    self.config_manager.set("query_headless_mode", self.parent_ui.query_headless_mode.get(), platform=current_platform)
                if hasattr(self.parent_ui, 'query_max_threads'):
                    self.config_manager.set("query_max_threads", self.parent_ui.query_max_threads.get(), platform=current_platform)

                # 强制保存配置
                result = self.config_manager.save_config(force=True)

                if result:
                    self.parent_ui.log("✅ 存稿设置已成功保存")
                else:
                    raise Exception("配置管理器保存失败")

            except Exception as e:
                self.parent_ui.log(f"❌ 存稿设置保存失败: {str(e)}")

                # 尝试直接保存配置作为备用方法
                try:
                    # 读取现有配置
                    existing_config = {}
                    if os.path.exists(config_file):
                        with open(config_file, 'r', encoding='utf-8') as f:
                            existing_config = json.load(f)

                    # 确保平台配置存在
                    if current_platform not in existing_config:
                        existing_config[current_platform] = {}

                    # 更新存稿设置
                    existing_config[current_platform].update({
                        "draft_limit": self.parent_ui.draft_limit.get(),
                        "loop_limit": self.parent_ui.loop_limit.get(),
                        "concurrent_accounts": self.parent_ui.concurrent_accounts.get(),
                        "random_video_allocation": self.parent_ui.random_video_allocation.get(),
                        "headless_mode": self.parent_ui.headless_mode.get(),
                        "archive_completed": self.parent_ui.archive_completed.get()
                    })

                    # 保存配置文件
                    with open(config_file, 'w', encoding='utf-8') as f:
                        json.dump(existing_config, f, ensure_ascii=False, indent=4)
                    self.parent_ui.log("✅ 存稿设置已通过备用方法保存")
                except Exception as backup_e:
                    self.parent_ui.log(f"❌ 备用保存方法也失败: {str(backup_e)}")

            # 更新存稿处理器配置
            if hasattr(self.parent_ui, 'draft_processor'):
                self.parent_ui.draft_processor.archive_completed = self.parent_ui.archive_completed.get()
                self.parent_ui.draft_processor.headless_mode = self.parent_ui.headless_mode.get()
                self.parent_ui.draft_processor.draft_limit = self.parent_ui.draft_limit.get()

        except Exception as e:
            self.parent_ui.log(f"❌ 保存存稿设置时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def cleanup(self):
        """清理设置管理器资源"""
        try:
            # 保存当前设置
            self.save_config()
        except Exception as e:
            self.parent_ui.log(f"❌ 清理设置管理器失败: {e}")
