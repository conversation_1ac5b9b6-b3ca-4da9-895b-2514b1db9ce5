"""
网易视频预处理工具 - 旧版本（已弃用）

⚠️ 此文件已弃用，请使用 main.py 作为新的启动入口
此程序是网易预处理功能的旧版本，已被模块化版本替代。
新版本启动文件：main.py
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 阻止旧版本运行
def show_deprecation_warning():
    """显示弃用警告"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    message = """⚠️ 此文件已弃用！

请使用新版本的启动文件：main.py

新版本具有以下优势：
• 模块化架构，更稳定
• 更好的性能和用户体验
• 修复了所有已知问题
• 与主程序更好的集成

请关闭此窗口并使用 main.py 启动视频处理工具。"""

    messagebox.showwarning("版本已弃用", message)
    root.destroy()
    sys.exit(0)

# 如果直接运行此文件，显示警告并退出
if __name__ == "__main__":
    show_deprecation_warning()

# 设置Tesseract OCR路径
try:
    import pytesseract
    pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
    print("Tesseract OCR路径已设置")
except Exception as e:
    print(f"Tesseract OCR设置失败: {{str(e)}}")

import os
import shutil
import uuid
import threading
import traceback
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext, font
from PIL import Image, ImageDraw, ImageFont
import time
import re
import subprocess

# 添加自动检测FFmpeg路径的支持

# 设置Tesseract路径（由修复工具添加）
def ensure_tesseract_available():
    """确保Tesseract OCR可用"""
    try:
        import pytesseract
        pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
        return True
    except Exception as e:
        print(f"Tesseract设置失败: {str(e)}")
        return False

def ensure_ffmpeg_available():
    """确保FFmpeg在系统中可用，如果不可用则尝试使用本地FFmpeg"""
    # 首先尝试将当前目录的ffmpeg添加到PATH中
    current_dir = os.path.dirname(os.path.abspath(__file__))
    local_ffmpeg_path = os.path.join(current_dir, "ffmpeg", "bin")
    local_ffmpeg_exe = os.path.join(local_ffmpeg_path, "ffmpeg.exe")

    # 如果本地FFmpeg存在，优先使用
    if os.path.exists(local_ffmpeg_exe):
        print(f"✅ 使用本地FFmpeg: {local_ffmpeg_exe}")
        # 确保添加到环境变量PATH的开头，优先级最高
        os.environ["PATH"] = local_ffmpeg_path + os.pathsep + os.environ.get("PATH", "")
        return True

    # 如果本地找不到，再尝试系统命令
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 系统已安装FFmpeg: " + result.stdout.splitlines()[0])
            return True
    except:
        print("⚠️ 系统未安装FFmpeg或不在PATH中，尝试查找其他位置...")

    # 检查多个可能的位置
    possible_paths = []

    # 1. 当前工作目录下的ffmpeg
    cwd_ffmpeg = os.path.join(os.getcwd(), "ffmpeg", "bin")

    # 2. 网易存稿分支目录下的ffmpeg
    branch_ffmpeg = os.path.join(os.getcwd(), "网易存稿分支", "ffmpeg", "bin")

    # 3. 其他可能的位置
    parent_dir = os.path.dirname(current_dir)
    parent_ffmpeg = os.path.join(parent_dir, "ffmpeg", "bin")

    # 添加所有可能的路径
    possible_paths.extend([
        branch_ffmpeg,   # 网易存稿分支/ffmpeg/bin
        cwd_ffmpeg,      # 当前目录/ffmpeg/bin
        parent_ffmpeg    # 父目录/ffmpeg/bin
    ])

    # 尝试所有可能的路径
    for path in possible_paths:
        ffmpeg_exe = os.path.join(path, "ffmpeg.exe")
        print(f"检查路径: {path}")
        if os.path.exists(ffmpeg_exe):
            print(f"✅ 找到本地FFmpeg: {ffmpeg_exe}")
            # 将FFmpeg路径添加到环境变量PATH中，使得子进程可以找到它
            os.environ["PATH"] = path + os.pathsep + os.environ.get("PATH", "")
            # 测试一下是否能用
            try:
                test_result = subprocess.run([ffmpeg_exe, "-version"], capture_output=True, text=True)
                if test_result.returncode == 0:
                    print("✅ FFmpeg测试成功!")
                    return True
                else:
                    print(f"⚠️ FFmpeg测试失败: {test_result.stderr}")
            except Exception as e:
                print(f"⚠️ FFmpeg测试异常: {str(e)}")
                continue

    print("❌ 未找到FFmpeg，某些功能可能无法正常工作")
    print("搜索的路径包括:")
    for path in possible_paths:
        print(f" - {path}")

    return False

# 诊断信息 - 打印Python环境信息
print(f"Python 版本: {sys.version}")
print(f"Python 路径: {sys.executable}")
print(f"当前工作目录: {os.getcwd()}")

# 检查已安装的包
print("\n=== 依赖库诊断信息 ===")

# 检查psutil
try:
    import psutil
    print(f"✅ psutil 已成功导入 (版本: {psutil.__version__})")

    # 显示系统信息
    print(f"系统信息: {psutil.cpu_count()}核CPU, {psutil.virtual_memory().total / (1024**3):.1f}GB内存")
except ImportError:
    print("❌ psutil 导入失败 - 将使用备选线程分配策略")
    try:
        # 尝试安装psutil
        print("正在尝试自动安装psutil...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "psutil"])
        print("psutil安装成功，请重启程序")
    except Exception as e:
        print(f"自动安装失败: {str(e)}")
        print("请手动运行: pip install psutil")

# 检查是否存在FFmpeg
ensure_ffmpeg_available()

# 简化MoviePy导入 - 只使用新版导入方式
print("\n尝试导入MoviePy...")
try:
    # 只使用新版MoviePy导入方式
    import moviepy
    from moviepy.video.io.VideoFileClip import VideoFileClip

    # 获取版本信息
    MOVIEPY_VERSION = getattr(moviepy, '__version__', 'unknown')
    print(f"✅ 成功导入MoviePy (版本: {MOVIEPY_VERSION})")
    print(f"✅ 使用导入路径: moviepy.video.io.VideoFileClip")
except ImportError as e:
    print(f"❌ 导入MoviePy失败: {str(e)}")
    print("正在尝试安装MoviePy...")

    try:
        # 尝试安装指定版本
        print("安装MoviePy 2.x版本...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "moviepy>=2.0.0"])

        # 再次尝试导入
        try:
            import moviepy
            from moviepy.video.io.VideoFileClip import VideoFileClip
            MOVIEPY_VERSION = getattr(moviepy, '__version__', 'unknown')
            print(f"✅ 安装并导入MoviePy成功 (版本: {MOVIEPY_VERSION})")
        except ImportError as e2:
            print(f"❌ 安装后导入仍然失败: {str(e2)}")
            messagebox.showerror("导入错误",
                               f"无法导入MoviePy库。\n\n错误信息: {str(e2)}\n\n请手动安装: pip install moviepy>=2.0.0")
            exit(1)
    except Exception as install_err:
        print(f"❌ 安装MoviePy时出错: {str(install_err)}")
        messagebox.showerror("安装错误",
                           f"安装MoviePy失败。\n\n错误信息: {str(install_err)}\n\n请手动安装: pip install moviepy>=2.0.0")
        exit(1)

print("=== 诊断信息结束 ===\n")

class VideoPreprocessApp:
    """视频预处理工具独立应用程序"""

    def __init__(self, root):
        """初始化应用程序"""
        self.root = root
        self.root.title("网易视频预处理工具 - 修复版 (Python 3.11.9)")
        self.root.geometry("1200x800")  # 更大的窗口尺寸
        self.root.minsize(1000, 700)    # 更大的最小窗口尺寸

        # 设置默认字体大小
        self.default_font_size = 12  # 默认字体大小
        self.configure_fonts()

        # 创建变量
        self.is_processing = False
        self.video_dir = tk.StringVar()
        self.processed_videos_dir = tk.StringVar()
        self.processed_covers_dir = tk.StringVar()

        # 添加变量修改跟踪
        self.video_dir.trace_add("write", self.update_output_dirs)

        # 视频处理相关设置
        self.video_min_duration = tk.IntVar(value=30)  # 最小视频时长(秒)
        self.video_max_duration = tk.IntVar(value=300)  # 最大视频时长(秒)
        self.video_ratio = tk.StringVar(value="16:9")  # 视频比例
        self.thread_num = tk.IntVar(value=4)  # 处理线程数

        # 封面文字设置 - 上方文字
        self.cover_text_top = tk.StringVar(value="")  # 左上角文字内容
        self.cover_color_top = tk.StringVar(value="#FFFFFF")  # 左上角文字颜色

        # 封面文字设置 - 下方文字
        self.cover_text_bottom = tk.StringVar(value="")  # 右下角文字内容
        self.cover_color_bottom = tk.StringVar(value="#FFFFFF")  # 右下角文字颜色

        # 共用的封面文字设置
        self.cover_size = tk.IntVar(value=60)  # 封面文字大小

        # 水印设置
        self.enable_watermark = tk.BooleanVar(value=False)  # 是否启用水印
        self.watermark_color = tk.StringVar(value="#FFFFFF")  # 水印颜色
        self.watermark_opacity = tk.DoubleVar(value=0.1)  # 水印透明度(0.0-1.0)
        self.watermark_position = tk.StringVar(value="全屏")  # 水印位置

        # 定义颜色选项和颜色名称映射
        self.color_options = [
            "#FFFFFF", "#000000", "#FF0000", "#0000FF", "#00FF00", "#FFFF00",
            "#FFA500", "#800080", "#FFC0CB", "#A52A2A", "#808080", "#00FFFF"
        ]

        self.color_names = {
            "#FFFFFF": "白色", "#000000": "黑色", "#FF0000": "红色",
            "#0000FF": "蓝色", "#00FF00": "绿色", "#FFFF00": "黄色",
            "#FFA500": "橙色", "#800080": "紫色", "#FFC0CB": "粉色",
            "#A52A2A": "棕色", "#808080": "灰色", "#00FFFF": "青色"
        }

        # 视频去重设置
        self.enable_deduplication = tk.BooleanVar(value=True)  # 启用视频去重
        self.auto_use_filename = tk.BooleanVar(value=True)  # 自动使用文件名作为标题

        # 初始化配置文件路径
        script_dir = os.path.dirname(os.path.abspath(__file__))
        config_filename = "preprocess_config.ini"
        self.config_file = os.path.join(script_dir, config_filename)

        # 记录要跟踪保存的变量
        self.tracked_vars = [
            self.video_dir, self.processed_videos_dir, self.processed_covers_dir,
            self.video_min_duration, self.video_max_duration, self.video_ratio,
            self.thread_num, self.cover_text_top, self.cover_color_top,
            self.cover_text_bottom, self.cover_color_bottom, self.cover_size,
            self.enable_deduplication, self.auto_use_filename,
            self.enable_watermark, self.watermark_color, self.watermark_opacity, self.watermark_position
        ]

        # 添加界面字体大小到tracked_vars
        self.font_size_var = tk.IntVar(value=self.default_font_size)
        self.tracked_vars.append(self.font_size_var)

        # 创建UI
        self.create_ui()

        # 加载配置
        self.load_config()

        # 更新字体大小
        if self.font_size_var.get() != self.default_font_size:
            self.default_font_size = self.font_size_var.get()
            self.configure_fonts()

        # 绑定关闭窗口事件，自动保存配置
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)

        # 自动保存定时器
        self.auto_save_interval = 60 * 1000  # 60秒
        self.schedule_auto_save()

        # 处理计数器
        self.success_count = 0
        self.failed_count = 0
        self.total_processed = 0

    def configure_fonts(self):
        """配置字体大小"""
        # 设置默认字体
        default_font = font.nametofont("TkDefaultFont")
        default_font.configure(size=self.default_font_size)
        self.root.option_add("*Font", default_font)

        # 设置文本字体
        text_font = font.nametofont("TkTextFont")
        text_font.configure(size=self.default_font_size)

        # 设置固定宽度字体
        fixed_font = font.nametofont("TkFixedFont")
        fixed_font.configure(size=self.default_font_size)

        # 其他字体
        self.root.option_add("*TCombobox*Listbox.Font", default_font)

    def increase_font_size(self):
        """增加字体大小"""
        self.default_font_size += 1
        self.font_size_var.set(self.default_font_size)
        self.configure_fonts()
        self.log(f"字体大小已增加到 {self.default_font_size}")
        self.save_config()

    def decrease_font_size(self):
        """减小字体大小"""
        if self.default_font_size > 8:  # 最小不低于8
            self.default_font_size -= 1
            self.font_size_var.set(self.default_font_size)
            self.configure_fonts()
            self.log(f"字体大小已减小到 {self.default_font_size}")
            self.save_config()

    def create_ui(self):
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)  # 增加边距

        # 创建上下分割的布局
        top_frame = ttk.Frame(main_frame)
        top_frame.pack(fill=tk.X, pady=(0, 15))

        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.BOTH, expand=True)

        # 字体控制按钮
        font_frame = ttk.Frame(top_frame)
        font_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(font_frame, text="界面字体大小:").pack(side=tk.LEFT)
        ttk.Button(font_frame, text="增大 (A+)", command=self.increase_font_size, width=10).pack(side=tk.LEFT, padx=5)
        ttk.Button(font_frame, text="减小 (A-)", command=self.decrease_font_size, width=10).pack(side=tk.LEFT, padx=5)
        self.font_size_label = ttk.Label(font_frame, text=f"当前大小: {self.default_font_size}")
        self.font_size_label.pack(side=tk.LEFT, padx=5)

        # 手动保存配置按钮
        ttk.Button(font_frame, text="保存配置", command=self.save_config, width=10).pack(side=tk.RIGHT, padx=5)

        # 添加状态显示区域
        status_frame = ttk.LabelFrame(top_frame, text="当前配置状态")
        status_frame.pack(fill=tk.X, padx=5, pady=5)

        # 创建状态信息标签
        self.status_text = tk.StringVar(value="准备就绪")
        self.status_label = ttk.Label(status_frame, textvariable=self.status_text,
                                     wraplength=1100, justify=tk.LEFT)
        self.status_label.pack(fill=tk.X, padx=10, pady=5)

        # 更新状态信息
        self.update_status_info()

        # 为关键变量添加追踪，以便在值变化时更新状态信息
        for var in self.tracked_vars:
            var.trace_add("write", self._create_trace_callback())

        # 在上部分创建设置区域
        settings_frame = ttk.LabelFrame(top_frame, text="基本设置")
        settings_frame.pack(fill=tk.X, padx=5, pady=5)

        # 第一行: 视频源目录
        source_frame = ttk.Frame(settings_frame)
        source_frame.pack(fill=tk.X, padx=10, pady=8)  # 增加内边距

        ttk.Label(source_frame, text="视频源目录:").pack(side=tk.LEFT)
        ttk.Entry(source_frame, textvariable=self.video_dir, width=60).pack(side=tk.LEFT, padx=10)
        ttk.Button(source_frame, text="浏览", command=self.select_video_dir, width=12).pack(side=tk.LEFT)

        # 第二行: 处理后视频和封面目录
        output_frame = ttk.Frame(settings_frame)
        output_frame.pack(fill=tk.X, padx=10, pady=8)

        ttk.Label(output_frame, text="处理后视频目录:").pack(side=tk.LEFT)
        ttk.Entry(output_frame, textvariable=self.processed_videos_dir, width=25).pack(side=tk.LEFT, padx=10)
        ttk.Button(output_frame, text="浏览",
                  command=lambda: self.select_directory(self.processed_videos_dir, "选择处理后视频目录"), width=8).pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(output_frame, text="封面目录:").pack(side=tk.LEFT)
        ttk.Entry(output_frame, textvariable=self.processed_covers_dir, width=25).pack(side=tk.LEFT, padx=10)
        ttk.Button(output_frame, text="浏览",
                  command=lambda: self.select_directory(self.processed_covers_dir, "选择处理后封面目录"), width=8).pack(side=tk.LEFT)

        # 第三行: 视频参数
        params_frame = ttk.Frame(settings_frame)
        params_frame.pack(fill=tk.X, padx=10, pady=8)

        ttk.Label(params_frame, text="视频时长:").pack(side=tk.LEFT)
        ttk.Spinbox(params_frame, from_=1, to=3600, textvariable=self.video_min_duration, width=6).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Label(params_frame, text="至").pack(side=tk.LEFT, padx=5)
        ttk.Spinbox(params_frame, from_=1, to=3600, textvariable=self.video_max_duration, width=6).pack(side=tk.LEFT)
        ttk.Label(params_frame, text="秒").pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(params_frame, text="视频比例:").pack(side=tk.LEFT, padx=(10, 0))
        ttk.Combobox(params_frame, textvariable=self.video_ratio, values=["16:9", "9:16", "4:3", "1:1"],
                    state="readonly", width=8).pack(side=tk.LEFT, padx=10)

        ttk.Label(params_frame, text="线程数:").pack(side=tk.LEFT, padx=(10, 0))
        ttk.Spinbox(params_frame, from_=1, to=32, textvariable=self.thread_num, width=4).pack(side=tk.LEFT, padx=10)

        # 去重选项
        ttk.Checkbutton(params_frame, text="视频去重", variable=self.enable_deduplication).pack(side=tk.LEFT, padx=10)

        # 封面设置模块
        cover_frame = ttk.LabelFrame(top_frame, text="封面文字设置")
        cover_frame.pack(fill=tk.X, padx=5, pady=5)

        # 自动使用文件名
        auto_frame = ttk.Frame(cover_frame)
        auto_frame.pack(fill=tk.X, padx=10, pady=8)

        ttk.Checkbutton(auto_frame, text="自动使用文件名作为标题文字", variable=self.auto_use_filename).pack(side=tk.LEFT)
        ttk.Label(auto_frame, text="(注意: 最多显示30个字符)").pack(side=tk.LEFT, padx=10)

        # 上方文字内容
        top_text_frame = ttk.Frame(cover_frame)
        top_text_frame.pack(fill=tk.X, padx=10, pady=8)

        ttk.Label(top_text_frame, text="左上角文字:").pack(side=tk.LEFT)
        ttk.Entry(top_text_frame, textvariable=self.cover_text_top, width=50).pack(side=tk.LEFT, padx=10)

        # 上方文字颜色
        ttk.Label(top_text_frame, text="颜色:").pack(side=tk.LEFT, padx=(10, 0))
        color_combo_top = ttk.Combobox(top_text_frame, textvariable=self.cover_color_top,
                    values=self.color_options,
                    state="readonly", width=10)
        color_combo_top.pack(side=tk.LEFT, padx=10)

        # 添加颜色预览框 - 上方文字
        self.color_preview_top = tk.Canvas(top_text_frame, width=20, height=20, bd=1, relief="raised")
        self.color_preview_top.pack(side=tk.LEFT, padx=5)
        self._update_color_preview(self.color_preview_top, self.cover_color_top.get())

        # 下方文字内容
        bottom_text_frame = ttk.Frame(cover_frame)
        bottom_text_frame.pack(fill=tk.X, padx=10, pady=8)

        ttk.Label(bottom_text_frame, text="右下角文字:").pack(side=tk.LEFT)
        ttk.Entry(bottom_text_frame, textvariable=self.cover_text_bottom, width=50).pack(side=tk.LEFT, padx=10)

        # 下方文字颜色
        ttk.Label(bottom_text_frame, text="颜色:").pack(side=tk.LEFT, padx=(10, 0))
        color_combo_bottom = ttk.Combobox(bottom_text_frame, textvariable=self.cover_color_bottom,
                    values=self.color_options,
                    state="readonly", width=10)
        color_combo_bottom.pack(side=tk.LEFT, padx=10)

        # 添加颜色预览框 - 下方文字
        self.color_preview_bottom = tk.Canvas(bottom_text_frame, width=20, height=20, bd=1, relief="raised")
        self.color_preview_bottom.pack(side=tk.LEFT, padx=5)
        self._update_color_preview(self.color_preview_bottom, self.cover_color_bottom.get())

        # 添加颜色名称提示
        color_names = {"#FFFFFF": "白色", "#000000": "黑色", "#FF0000": "红色",
                       "#0000FF": "蓝色", "#00FF00": "绿色", "#FFFF00": "黄色"}

        def on_color_selected(event, position="top", preview_canvas=None):
            combo = event.widget
            color = combo.get()

            # 更新预览色块
            if preview_canvas and color:
                self._update_color_preview(preview_canvas, color)

            # 记录日志
            if color in self.color_names:
                self.log(f"已选择{position}文字颜色: {self.color_names[color]}")

        # 绑定颜色选择事件 - 更新预览
        color_combo_top.bind("<<ComboboxSelected>>", lambda e: on_color_selected(e, "左上角", self.color_preview_top))
        color_combo_bottom.bind("<<ComboboxSelected>>", lambda e: on_color_selected(e, "右下角", self.color_preview_bottom))

        # 字体大小
        size_frame = ttk.Frame(cover_frame)
        size_frame.pack(fill=tk.X, padx=10, pady=8)

        # 字体大小标题和输入框
        size_left_frame = ttk.Frame(size_frame)
        size_left_frame.pack(side=tk.LEFT, fill=tk.Y)

        ttk.Label(size_left_frame, text="字体大小:").pack(side=tk.TOP, anchor=tk.W, pady=(0, 5))

        # 创建字体大小输入框
        font_spinner = ttk.Spinbox(size_left_frame, from_=20, to=100, textvariable=self.cover_size, width=5)
        font_spinner.pack(side=tk.TOP, pady=2)
        font_spinner.bind("<Return>", lambda e: self._update_font_size_preview())
        font_spinner.bind("<<Increment>>", lambda e: self.root.after(10, self._update_font_size_preview))
        font_spinner.bind("<<Decrement>>", lambda e: self.root.after(10, self._update_font_size_preview))

        # 预览区域 - 放在右侧，单独的框架中
        preview_frame = ttk.LabelFrame(size_frame, text="字体大小预览")
        preview_frame.pack(side=tk.LEFT, padx=(30, 0), pady=5)

        # 创建字体预览画布 - 增加尺寸使其更容易看清
        self.font_preview_canvas = tk.Canvas(preview_frame, width=180, height=60, bg="white", bd=1, relief="sunken")
        self.font_preview_canvas.pack(padx=10, pady=10)

        # 初始预览
        self._update_font_size_preview()

        # 水印设置模块
        watermark_frame = ttk.LabelFrame(top_frame, text="封面水印设置")
        watermark_frame.pack(fill=tk.X, padx=5, pady=5)

        # 启用水印选项
        enable_frame = ttk.Frame(watermark_frame)
        enable_frame.pack(fill=tk.X, padx=10, pady=8)

        ttk.Checkbutton(enable_frame, text="启用封面隐形水印", variable=self.enable_watermark).pack(side=tk.LEFT)
        ttk.Label(enable_frame, text="(仅添加到封面图像，不影响视频)").pack(side=tk.LEFT, padx=10)

        # 水印颜色和透明度
        color_frame = ttk.Frame(watermark_frame)
        color_frame.pack(fill=tk.X, padx=10, pady=8)

        ttk.Label(color_frame, text="水印颜色:").pack(side=tk.LEFT)
        color_combo = ttk.Combobox(color_frame, textvariable=self.watermark_color,
                                 values=self.color_options,
                                 state="readonly", width=10)
        color_combo.pack(side=tk.LEFT, padx=10)

        # 添加颜色预览框
        self.watermark_color_preview = tk.Canvas(color_frame, width=20, height=20, bd=1, relief="raised")
        self.watermark_color_preview.pack(side=tk.LEFT, padx=5)
        self._update_color_preview(self.watermark_color_preview, self.watermark_color.get())

        # 水印透明度
        ttk.Label(color_frame, text="透明度:").pack(side=tk.LEFT, padx=(20, 5))
        opacity_spinner = ttk.Spinbox(color_frame, from_=0.01, to=0.5, increment=0.01,
                                    textvariable=self.watermark_opacity, width=5)
        opacity_spinner.pack(side=tk.LEFT, padx=5)
        ttk.Label(color_frame, text="(0.01-0.5，越小越不明显)").pack(side=tk.LEFT, padx=5)

        # 水印位置
        position_frame = ttk.Frame(watermark_frame)
        position_frame.pack(fill=tk.X, padx=10, pady=8)

        ttk.Label(position_frame, text="水印位置:").pack(side=tk.LEFT)
        ttk.Combobox(position_frame, textvariable=self.watermark_position,
                   values=["全屏", "左上角", "右上角", "左下角", "右下角", "中心"],
                   state="readonly", width=10).pack(side=tk.LEFT, padx=10)

        # 绑定颜色选择事件
        color_combo.bind("<<ComboboxSelected>>",
                      lambda e: self._update_color_preview(self.watermark_color_preview, self.watermark_color.get()))

        # 添加说明标签
        ttk.Label(watermark_frame, text="注意: 水印仅应用于封面图像，不会影响视频文件。",
                font=("TkDefaultFont", self.default_font_size - 1), foreground="#666666").pack(padx=10, pady=(0, 8))

        # 按钮区域
        button_frame = ttk.Frame(top_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=10)

        # 创建自定义样式
        style = ttk.Style()
        style.configure("Start.TButton", font=("TkDefaultFont", self.default_font_size + 2, "bold"))
        style.configure("Stop.TButton", font=("TkDefaultFont", self.default_font_size + 2))

        ttk.Button(button_frame, text="开始处理", command=self.start_preprocess,
                  style="Start.TButton", width=15).pack(side=tk.RIGHT, padx=10)
        ttk.Button(button_frame, text="停止处理", command=self.stop_processing,
                  style="Stop.TButton", width=15).pack(side=tk.RIGHT, padx=10)

        # 先创建日志区域（但不立即显示）
        self.log_frame = ttk.LabelFrame(bottom_frame, text="处理日志")

        # 创建可视化框架（要显示在日志区域上方）
        self.visualization_frame = ttk.LabelFrame(bottom_frame, text="处理进度")

        # 进度条区域
        self.progress_frame = ttk.Frame(self.visualization_frame)
        self.progress_frame.pack(fill=tk.X, padx=10, pady=(10, 0))

        # 创建进度条
        ttk.Label(self.progress_frame, text="处理进度:").pack(side=tk.LEFT, padx=(0, 10))
        self.progress_bar = ttk.Progressbar(self.progress_frame, orient="horizontal", length=600, mode="determinate")
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.progress_label = ttk.Label(self.progress_frame, text="0%")
        self.progress_label.pack(side=tk.LEFT, padx=(10, 0))

        # 状态统计区域
        self.stats_frame = ttk.Frame(self.visualization_frame)
        self.stats_frame.pack(fill=tk.X, padx=10, pady=(5, 10))

        # 统计标签
        stats_style = ttk.Style()
        stats_style.configure("Success.TLabel", foreground="green")
        stats_style.configure("Fail.TLabel", foreground="red")

        ttk.Label(self.stats_frame, text="总计:").pack(side=tk.LEFT)
        self.total_label = ttk.Label(self.stats_frame, text="0")
        self.total_label.pack(side=tk.LEFT, padx=(5, 15))

        ttk.Label(self.stats_frame, text="成功:").pack(side=tk.LEFT)
        self.success_label = ttk.Label(self.stats_frame, text="0", style="Success.TLabel")
        self.success_label.pack(side=tk.LEFT, padx=(5, 15))

        ttk.Label(self.stats_frame, text="失败:").pack(side=tk.LEFT)
        self.fail_label = ttk.Label(self.stats_frame, text="0", style="Fail.TLabel")
        self.fail_label.pack(side=tk.LEFT, padx=(5, 15))

        # 创建大字体日志文本区域
        log_font = font.Font(family="Courier New", size=self.default_font_size)
        self.log_text = scrolledtext.ScrolledText(self.log_frame, wrap=tk.WORD, width=80, height=20, font=log_font)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.log_text.config(state=tk.DISABLED)

        # 设置初始布局 - 只显示日志框架，不显示可视化框架
        self.log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 添加初始日志
        self.log("欢迎使用网易视频预处理工具 - 修复版")
        self.log("此版本适配了新版MoviePy库，并支持调整界面字体大小")
        self.log("您可以使用界面顶部的'增大(A+)'和'减小(A-)'按钮来调整界面字体大小")
        self.log("自动保存功能已启用，配置会每隔60秒自动保存")
        self.log("请设置视频源目录和输出目录，然后点击'开始处理'")

    def log(self, message):
        """添加日志消息"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.root.update_idletasks()

    def update_output_dirs(self, *args):
        """当视频源目录变化时更新输出目录"""
        # 获取当前视频源目录
        video_dir = self.video_dir.get()
        if not video_dir:
            return

        # 构建推荐的输出目录路径
        processed_videos_dir = os.path.join(video_dir, "processed_videos")
        processed_covers_dir = os.path.join(video_dir, "processed_covers")

        # 如果输出目录为空或与旧的视频目录相关，则自动更新
        current_videos_dir = self.processed_videos_dir.get()
        current_covers_dir = self.processed_covers_dir.get()

        # 自动更新视频输出目录
        if not current_videos_dir or self._is_related_to_old_dir(current_videos_dir, video_dir):
            self.processed_videos_dir.set(processed_videos_dir)

        # 自动更新封面输出目录
        if not current_covers_dir or self._is_related_to_old_dir(current_covers_dir, video_dir):
            self.processed_covers_dir.set(processed_covers_dir)

    def _is_related_to_old_dir(self, current_path, new_parent_dir):
        """检查当前路径是否与旧的父目录相关"""
        # 如果当前路径是新父目录的子目录，则不需要更新
        if current_path.startswith(new_parent_dir):
            return False

        # 检查是否包含标准子目录名
        basename = os.path.basename(current_path)
        if basename in ["processed_videos", "processed_covers"]:
            return True

        return False

    def select_video_dir(self):
        """选择视频源目录"""
        directory = filedialog.askdirectory(title="选择视频源目录")
        if directory:
            old_dir = self.video_dir.get()

            # 设置新目录
            self.video_dir.set(directory)

            # 自动设置输出目录 - update_output_dirs回调会被触发
            # 但在这里也做一些额外的检查

            # 如果输出目录为空或不存在，则自动设置
            processed_videos_dir = os.path.join(directory, "processed_videos")
            processed_covers_dir = os.path.join(directory, "processed_covers")

            # 仅当路径不存在时才额外提示
            if not os.path.exists(self.processed_videos_dir.get()):
                self.log(f"注意: 处理后视频目录 '{self.processed_videos_dir.get()}' 不存在，将在处理时自动创建")

            if not os.path.exists(self.processed_covers_dir.get()):
                self.log(f"注意: 处理后封面目录 '{self.processed_covers_dir.get()}' 不存在，将在处理时自动创建")

            # 立即保存配置，确保路径设置被保存
            self.save_config()

            # 记录日志
            self.log(f"已选择视频源目录: {directory}")

    def select_directory(self, var, title):
        """通用目录选择方法"""
        directory = filedialog.askdirectory(title=title)
        if directory:
            var.set(directory)
            self.log(f"已选择目录: {directory}")

    def stop_processing(self):
        """停止视频处理"""
        if self.is_processing:
            self.log("⏱️ 正在停止处理，请稍等...")
            self.is_processing = False

    def process_video_file(self, video_file, video_dir, processed_videos_dir, processed_covers_dir, min_duration, max_duration):
        """处理单个视频文件"""
        try:
            # 视频文件完整路径
            video_path = os.path.join(video_dir, video_file)

            # 检查视频是否存在
            if not os.path.exists(video_path):
                return {
                    'success': False,
                    'message': f"视频文件不存在: {video_path}"
                }

            # 处理文件名（保持原始文件名）
            file_name = os.path.splitext(video_file)[0]  # 文件名（不含扩展名）
            file_ext = os.path.splitext(video_file)[1]   # 扩展名

            # 清理文件名 - 删除前缀特殊符号和后缀随机字符
            # 1. 移除前缀特殊符号如 #
            while file_name and file_name[0] in '#@*_-+.,:;!?':
                file_name = file_name[1:]

            # 2. 移除末尾的随机字符串格式如 _4f38e739
            file_name = re.sub(r'_[a-zA-Z0-9]{6,10}$', '', file_name)

            # 3. 去除多余空格，修复其他潜在问题
            file_name = file_name.strip()

            # 如果开启了视频去重，生成唯一文件名
            if self.enable_deduplication.get():
                # 使用UUID确保唯一性，但不直接显示在文件名中
                short_uuid = str(uuid.uuid4())[:8]
                base_name = file_name[:30] if len(file_name) > 30 else file_name

                # 隐藏UUID到文件名末尾隐藏属性中，不改变可见文件名
                processed_name = base_name

                # 仅当检测到重名时才添加UUID
                test_path = os.path.join(processed_videos_dir, f"{processed_name}{file_ext}")
                if os.path.exists(test_path):
                    processed_name = f"{base_name}_{short_uuid}"

                truncated_name = f"{processed_name}{file_ext}"
            else:
                # 如果不开启去重，仍然确保文件名不会太长
                if len(file_name) > 30:
                    truncated_name = f"{file_name[:30]}{file_ext}"
                else:
                    truncated_name = f"{file_name}{file_ext}"

            # 设置输出路径
            output_path = os.path.join(processed_videos_dir, truncated_name)
            cover_name = f"{os.path.splitext(truncated_name)[0]}.jpg"
            cover_path = os.path.join(processed_covers_dir, cover_name)

            # 检查输出文件是否已存在 (在非去重模式下可能发生)
            if os.path.exists(output_path):
                # 生成新的文件名，但保持干净的命名方式
                base_name = os.path.splitext(truncated_name)[0]
                count = 1

                # 尝试添加序号直到找到可用的文件名
                while os.path.exists(output_path):
                    new_name = f"{base_name}_{count}"
                    truncated_name = f"{new_name}{file_ext}"
                    output_path = os.path.join(processed_videos_dir, truncated_name)
                    count += 1

                # 更新封面路径
                cover_name = f"{os.path.splitext(truncated_name)[0]}.jpg"
                cover_path = os.path.join(processed_covers_dir, cover_name)

            # 使用ffmpeg快速获取视频信息（比moviepy更快）
            try:
                import subprocess
                import json

                # 优先使用ffprobe快速获取视频信息
                try:
                    # 使用ffprobe获取视频信息
                    cmd = [
                        'ffprobe',
                        '-v', 'error',
                        '-select_streams', 'v:0',
                        '-show_entries', 'stream=width,height,duration',
                        '-of', 'json',
                        video_path
                    ]

                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode == 0:
                        video_info = json.loads(result.stdout)
                        stream = video_info.get('streams', [{}])[0]

                        width = int(stream.get('width', 0))
                        height = int(stream.get('height', 0))
                        duration_seconds = float(stream.get('duration', 0))

                        if width > 0 and height > 0 and duration_seconds > 0:
                            # 检查视频时长
                            min_seconds = min_duration
                            max_seconds = max_duration

                            if duration_seconds < min_seconds:
                                return {
                                    'success': False,
                                    'message': f"视频 {video_file} 时长过短: {duration_seconds:.2f}秒 < {min_seconds}秒"
                                }

                            if duration_seconds > max_seconds:
                                return {
                                    'success': False,
                                    'message': f"视频 {video_file} 时长过长: {duration_seconds:.2f}秒 > {max_seconds}秒"
                                }

                            # 检查视频比例
                            aspect_ratio = width / height
                            selected_ratio = self.video_ratio.get()

                            if selected_ratio == "16:9":
                                target_ratio = 16 / 9
                            elif selected_ratio == "4:3":
                                target_ratio = 4 / 3
                            elif selected_ratio == "1:1":
                                target_ratio = 1
                            elif selected_ratio == "9:16":
                                target_ratio = 9 / 16
                            else:
                                target_ratio = 16 / 9  # 默认16:9

                            # 容差为0.1
                            if abs(aspect_ratio - target_ratio) > 0.1:
                                return {
                                    'success': False,
                                    'message': f"视频 {video_file} 比例不符合要求: {aspect_ratio:.2f}，期望: {target_ratio:.2f}"
                                }

                            # 提取封面（使用ffmpeg）
                            cover_time = min(duration_seconds - 0.5, 5.0) if duration_seconds > 5.0 else duration_seconds / 2
                            cover_cmd = [
                                'ffmpeg',
                                '-ss', str(cover_time),
                                '-i', video_path,
                                '-vframes', '1',
                                '-q:v', '2',
                                cover_path
                            ]

                            cover_result = subprocess.run(cover_cmd, capture_output=True)

                            # 检查封面是否成功提取
                            if cover_result.returncode == 0 and os.path.exists(cover_path):
                                # 处理封面文字
                                self._add_cover_text(cover_path, os.path.splitext(video_file)[0])

                                # 添加封面水印
                                self._add_watermark_to_cover(cover_path)

                                # 不再根据水印设置条件处理视频，直接复制视频文件
                                self._add_watermark_to_video(video_path, output_path)
                                return {
                                    'success': True,
                                    'message': f"✅ 视频 {video_file} 处理成功，已保存为: {truncated_name}",
                                    'file': video_file
                                }

                            # 如果ffmpeg提取封面失败，回退到moviepy方法
                            raise Exception("ffmpeg提取封面失败，回退到moviepy")
                        else:
                            raise Exception("无法从ffprobe获取完整视频信息")
                    else:
                        raise Exception(f"ffprobe返回错误: {result.stderr}")

                except Exception as ffmpeg_err:
                    # 如果ffprobe失败，继续尝试moviepy
                    print(f"ffprobe处理失败，回退到moviepy: {str(ffmpeg_err)}")

                # 使用moviepy作为备选方案
                with VideoFileClip(video_path) as video:
                    duration_seconds = video.duration
                    duration_minutes = duration_seconds / 60
                    width, height = video.size

                    # 检查视频时长
                    min_seconds = min_duration
                    max_seconds = max_duration

                    if duration_seconds < min_seconds:
                        return {
                            'success': False,
                            'message': f"视频 {video_file} 时长过短: {duration_seconds:.2f}秒 < {min_seconds}秒"
                        }

                    if duration_seconds > max_seconds:
                        return {
                            'success': False,
                            'message': f"视频 {video_file} 时长过长: {duration_seconds:.2f}秒 > {max_seconds}秒"
                        }

                    # 检查视频比例
                    aspect_ratio = width / height
                    selected_ratio = self.video_ratio.get()

                    if selected_ratio == "16:9":
                        target_ratio = 16 / 9
                    elif selected_ratio == "4:3":
                        target_ratio = 4 / 3
                    elif selected_ratio == "1:1":
                        target_ratio = 1
                    elif selected_ratio == "9:16":
                        target_ratio = 9 / 16
                    else:
                        target_ratio = 16 / 9  # 默认16:9

                    # 容差为0.1
                    if abs(aspect_ratio - target_ratio) > 0.1:
                        return {
                            'success': False,
                            'message': f"视频 {video_file} 比例不符合要求: {aspect_ratio:.2f}，期望: {target_ratio:.2f}"
                        }

                    # 提取封面
                    # 获取视频中间帧作为封面
                    cover_time = min(video.duration - 0.5, 5.0) if video.duration > 5.0 else video.duration / 2
                    frame = video.get_frame(cover_time)

                    # 保存封面
                    cover_img = Image.fromarray(frame)
                    cover_img.save(cover_path, quality=95)

                    # 处理封面文字（作为单独函数）
                    self._add_cover_text(cover_path, os.path.splitext(video_file)[0])

                    # 添加封面水印
                    self._add_watermark_to_cover(cover_path)

            except Exception as e:
                return {
                    'success': False,
                    'message': f"处理视频 {video_file} 时出错: {str(e)}"
                }

            # 复制视频文件
            shutil.copy2(video_path, output_path)
            return {
                'success': True,
                'message': f"✅ 视频 {video_file} 处理成功，已保存为: {truncated_name}",
                'file': video_file
            }

        except Exception as e:
            return {
                'success': False,
                'message': f"处理视频 {video_file} 失败: {str(e)}"
            }

    def _add_cover_text(self, cover_path, base_filename):
        """添加文字到封面图片，分离为单独函数以提高代码清晰度"""
        try:
            # 打开图片
            cover_img = Image.open(cover_path)
            img_width, img_height = cover_img.size

            # 准备封面文字
            top_text = self.cover_text_top.get()
            bottom_text = self.cover_text_bottom.get()

            # 智能拆分文件名的辅助函数
            def smart_split_filename(filename, max_total_chars=30):
                # 清理文件名 - 删除前缀特殊符号和后缀随机字符
                # 1. 移除前缀特殊符号如 #
                while filename and filename[0] in '#@*_-+.,:;!?':
                    filename = filename[1:].lstrip()

                # 2. 移除末尾的随机字符串格式如 _4f38e739
                filename = re.sub(r'_[a-zA-Z0-9]{6,10}$', '', filename)

                # 3. 去除多余空格，修复其他潜在问题
                filename = filename.strip()

                # 首先确保文件名总长度不超过限制
                if len(filename) > max_total_chars:
                    filename = filename[:max_total_chars]

                # 如果文件名很短，直接返回
                if len(filename) <= 15:  # 小于15个字符时不拆分
                    return filename, ""

                # 查找拆分点 - 优先在标点符号处拆分
                punctuation = ['.', '_', '-', ' ', '、', '，', '。', '：', '；', '!', '?', '！', '？',
                              '(', ')', '[', ']', '{', '}', '（', '）', '【', '】', '「', '」',
                              '《', '》', '〈', '〉', '"', '"', ''', ''', ',', ';', ':', '/', '\\']

                # 寻找中间点附近的分隔符
                mid_point = len(filename) // 2
                best_split_point = mid_point
                min_distance = len(filename)

                # 在文件名中寻找最接近中点的标点符号
                for i, char in enumerate(filename):
                    if char in punctuation:
                        distance = abs(i - mid_point)
                        if distance < min_distance:
                            min_distance = distance
                            best_split_point = i + 1  # 在标点符号后拆分

                # 如果没有找到合适的标点符号，尝试寻找中文与非中文字符的分界点
                if best_split_point == mid_point:
                    for i in range(mid_point - 5, mid_point + 5):
                        if 0 < i < len(filename) - 1:
                            # 检查是否是中文与非中文的分界
                            curr_is_chinese = '\u4e00' <= filename[i] <= '\u9fff'
                            next_is_chinese = '\u4e00' <= filename[i+1] <= '\u9fff'
                            if curr_is_chinese != next_is_chinese:
                                best_split_point = i + 1
                                break

                # 拆分文件名
                first_part = filename[:best_split_point]
                second_part = filename[best_split_point:]

                return first_part, second_part

            # 根据设置和用户输入决定如何处理文字
            if self.auto_use_filename.get():
                # 预清理文件名，移除特殊字符和随机后缀
                clean_filename = base_filename

                # 如果开启了自动使用文件名功能
                if not top_text and not bottom_text:
                    # 如果用户没有填写任何文字，则智能拆分文件名为上下两部分
                    if len(clean_filename) > 0:
                        top_text, bottom_text = smart_split_filename(clean_filename)
                elif not top_text:
                    # 如果只有左上角文字为空，使用文件名前半部分
                    first_part, _ = smart_split_filename(clean_filename)
                    top_text = first_part
                elif not bottom_text:
                    # 如果只有右下角文字为空，使用文件名后半部分
                    if len(clean_filename) > 0:
                        _, second_part = smart_split_filename(clean_filename)
                        bottom_text = second_part
            elif not top_text and not bottom_text:
                # 当用户未填写左上和右下文字且未开启自动使用文件名时，智能拆分文件名
                # 预清理文件名，移除特殊字符和随机后缀
                clean_filename = base_filename
                if len(clean_filename) > 0:
                    top_text, bottom_text = smart_split_filename(clean_filename)

            # 保存原始的设定字体大小
            original_font_size = int(self.cover_size.get())

            # 绘制封面文字
            if top_text or bottom_text:
                draw = ImageDraw.Draw(cover_img)

                # 加载字体的函数
                def load_font(size):
                    try:
                        # 尝试加载微软雅黑粗体字体
                        try:
                            return ImageFont.truetype("msyhbd.ttc", size)
                        except:
                            try:
                                return ImageFont.truetype("msyh.ttc", size)
                            except:
                                try:
                                    return ImageFont.truetype("arial.ttf", size)
                                except:
                                    return ImageFont.load_default()
                    except:
                        return ImageFont.load_default()

                # 检查文字是否超出边界，如果超出，则减小字体大小
                def adjust_font_size(text, max_width, initial_size, min_size=20):
                    # 如果文本为空，直接返回
                    if not text:
                        font = load_font(initial_size)
                        return font, 0, 0, ""

                    # 初始字体大小
                    font_size = initial_size
                    font = load_font(font_size)

                    # 获取文字大小
                    bbox = draw.textbbox((0, 0), text, font=font)
                    text_width = bbox[2] - bbox[0]

                    # 如果文字宽度超过最大宽度，逐渐减小字体大小
                    while text_width > max_width and font_size > min_size:
                        font_size -= 2  # 每次减小2个点
                        font = load_font(font_size)
                        bbox = draw.textbbox((0, 0), text, font=font)
                        text_width = bbox[2] - bbox[0]

                    # 如果字体已经最小，但文本仍然太长，进行截断
                    if text_width > max_width:
                        # 智能截断，尝试保留语义
                        # 中文字符优先保留完整词组
                        has_chinese = any('\u4e00' <= char <= '\u9fff' for char in text)

                        if has_chinese:
                            # 中文文本按字符截断比较合理
                            ratio = max_width / text_width
                            keep_chars = max(int(len(text) * ratio * 0.9), 6)  # 至少保留6个字符

                            if keep_chars < len(text):
                                # 确保不会在中文字符中间截断
                                if keep_chars < len(text) - 3:
                                    text = text[:keep_chars] + "..."
                                else:
                                    # 如果只剩下几个字符，干脆全部显示
                                    pass
                        else:
                            # 英文文本尝试在空格处截断
                            words = text.split()
                            truncated = []
                            current_width = 0

                            for word in words:
                                word_bbox = draw.textbbox((0, 0), word + " ", font=font)
                                word_width = word_bbox[2] - word_bbox[0]

                                if current_width + word_width <= max_width * 0.9:
                                    truncated.append(word)
                                    current_width += word_width
                                else:
                                    break

                            if truncated and len(truncated) < len(words):
                                text = " ".join(truncated) + "..."
                            elif len(text) > 15:  # 如果是一个很长的单词
                                ratio = max_width / text_width
                                keep_chars = max(int(len(text) * ratio * 0.9), 12)
                                text = text[:keep_chars] + "..."

                        # 重新测量截断后的宽度
                        bbox = draw.textbbox((0, 0), text, font=font)
                        text_width = bbox[2] - bbox[0]

                    # 最后一次检查，极端情况处理
                    if text_width > max_width and len(text) > 10:
                        # 强制截断
                        text = text[:7] + "..."
                        bbox = draw.textbbox((0, 0), text, font=font)
                        text_width = bbox[2] - bbox[0]

                    return font, text_width, bbox[3] - bbox[1], text  # 返回字体对象、文字宽度、高度和可能被截断的文本

                # 颜色映射
                color_map = {
                    "#FFFFFF": (255, 255, 255),  # 白色
                    "#000000": (0, 0, 0),        # 黑色
                    "#FF0000": (255, 0, 0),      # 红色
                    "#0000FF": (0, 0, 255),      # 蓝色
                    "#00FF00": (0, 255, 0),      # 绿色
                    "#FFFF00": (255, 255, 0),    # 黄色
                    "#FFA500": (255, 165, 0),    # 橙色
                    "#800080": (128, 0, 128),    # 紫色
                    "#FFC0CB": (255, 192, 203),  # 粉色
                    "#A52A2A": (165, 42, 42),    # 棕色
                    "#808080": (128, 128, 128),  # 灰色
                    "#00FFFF": (0, 255, 255)     # 青色
                }

                # 留出边距
                margin = 20
                max_text_width = img_width - (margin * 2)

                # 绘制左上角文字
                if top_text:
                    text_color = color_map.get(self.cover_color_top.get(), (255, 255, 255))

                    # 获取最佳字体大小和文字尺寸，以及可能被截断的文本
                    top_font, text_width, text_height, truncated_top_text = adjust_font_size(top_text, max_text_width, original_font_size)

                    # 左上角位置
                    text_x = margin
                    text_y = margin

                    # 绘制文字阴影
                    shadow_offset = 2
                    draw.text((text_x + shadow_offset, text_y + shadow_offset),
                             truncated_top_text, font=top_font, fill=(0, 0, 0))

                    # 绘制文字
                    draw.text((text_x, text_y), truncated_top_text, font=top_font, fill=text_color)

                # 绘制右下角文字
                if bottom_text:
                    text_color = color_map.get(self.cover_color_bottom.get(), (255, 255, 255))

                    # 获取最佳字体大小和文字尺寸，以及可能被截断的文本
                    bottom_font, text_width, text_height, truncated_bottom_text = adjust_font_size(bottom_text, max_text_width, original_font_size)

                    # 右下角位置
                    text_x = img_width - text_width - margin
                    text_y = img_height - text_height - margin

                    # 确保文字不会超出图片边界
                    text_x = max(margin, text_x)
                    text_y = max(margin, text_y)

                    # 绘制文字阴影
                    shadow_offset = 2
                    draw.text((text_x + shadow_offset, text_y + shadow_offset),
                             truncated_bottom_text, font=bottom_font, fill=(0, 0, 0))

                    # 绘制文字
                    draw.text((text_x, text_y), truncated_bottom_text, font=bottom_font, fill=text_color)

            # 保存封面
            cover_img.save(cover_path, quality=95)
            return True
        except Exception as e:
            print(f"添加封面文字失败: {str(e)}")
            return False

    def update_progress_ui(self, current, total, success_count, failed_count):
        """更新进度条和统计信息"""
        if not hasattr(self, 'progress_bar') or not hasattr(self, 'progress_label'):
            return

        # 确保可视化框架可见并位于日志框架之上
        if self.visualization_frame.winfo_manager() != 'pack':
            # 确保两个框架都从父容器中移除
            if self.log_frame.winfo_manager() == 'pack':
                self.log_frame.pack_forget()
            if self.visualization_frame.winfo_manager() == 'pack':
                self.visualization_frame.pack_forget()

            # 按正确顺序显示：先可视化框架，后日志框架
            self.visualization_frame.pack(fill=tk.X, padx=5, pady=5)
            self.log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 计算进度百分比
        if total > 0:
            percent = int((current / total) * 100)
        else:
            percent = 0

        # 更新进度条
        self.progress_bar["value"] = percent
        self.progress_label["text"] = f"{percent}%"

        # 更新统计数据
        self.total_label["text"] = str(total)
        self.success_label["text"] = str(success_count)
        self.fail_label["text"] = str(failed_count)

        # 刷新UI
        self.root.update_idletasks()

    def reset_progress_ui(self):
        """重置进度条和统计信息"""
        if hasattr(self, 'progress_bar'):
            # 重置进度条和标签
            self.progress_bar["value"] = 0
            self.progress_label["text"] = "0%"
            self.total_label["text"] = "0"
            self.success_label["text"] = "0"
            self.fail_label["text"] = "0"

            # 隐藏可视化框架
            if hasattr(self, 'visualization_frame') and self.visualization_frame.winfo_manager() == 'pack':
                self.visualization_frame.pack_forget()
                # 确保日志框架仍然正确显示
                if hasattr(self, 'log_frame') and self.log_frame.winfo_manager() != 'pack':
                    self.log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def preprocess_task(self):
        """预处理视频任务"""
        try:
            self.is_processing = True
            # 重置计数器
            self.success_count = 0
            self.failed_count = 0
            self.total_processed = 0

            # 重置进度条和UI
            self.reset_progress_ui()

            self.log("\n=== 开始预处理视频 ===")

            # 获取视频目录
            video_dir = self.video_dir.get()
            if not os.path.exists(video_dir):
                self.log(f"❌ 视频目录不存在: {video_dir}")
                self.is_processing = False
                return

            # 检查ffmpeg是否可用
            ffmpeg_available = False
            try:
                import subprocess
                result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
                if result.returncode == 0:
                    ffmpeg_available = True
                    self.log("✅ 检测到ffmpeg，将使用ffmpeg进行高速处理")
                else:
                    self.log("⚠️ 未检测到ffmpeg，将使用moviepy处理（速度较慢）")
            except:
                self.log("⚠️ 未检测到ffmpeg，将使用moviepy处理（速度较慢）")

            # 如果启用了水印但FFmpeg不可用，显示警告
            if self.enable_watermark.get() and not ffmpeg_available:
                self.log("⚠️ 警告: 启用了水印功能，但未检测到FFmpeg。水印功能需要FFmpeg支持！")
                messagebox.showwarning("水印功能警告", "您启用了水印功能，但系统未检测到FFmpeg。\n\n水印功能需要FFmpeg支持，可能无法正常工作！")

            # 检查视频目录内容
            self.log(f"📂 检查视频目录: {video_dir}")
            video_extensions = ('.mp4', '.avi', '.mkv', '.mov', '.flv', '.wmv', '.webm')
            found_files = [f for f in os.listdir(video_dir) if any(f.lower().endswith(ext) for ext in video_extensions)]

            if not found_files:
                self.log("❌ 视频目录中没有找到视频文件")
                self.is_processing = False
                return

            self.log(f"📊 目录中找到 {len(found_files)} 个视频文件")
            # 只显示前10个文件的名称
            for file in found_files[:10]:
                self.log(f"   - {file}")
            if len(found_files) > 10:
                self.log(f"   ... 等 {len(found_files)-10} 个文件")

            # 获取处理后视频目录
            processed_videos_dir = self.processed_videos_dir.get()
            if not os.path.exists(processed_videos_dir):
                os.makedirs(processed_videos_dir)
                self.log(f"✅ 创建了处理后视频目录: {processed_videos_dir}")

            # 获取处理后封面目录
            processed_covers_dir = self.processed_covers_dir.get()
            if not os.path.exists(processed_covers_dir):
                os.makedirs(processed_covers_dir)
                self.log(f"✅ 创建了处理后封面目录: {processed_covers_dir}")

            # 显示水印设置信息
            if self.enable_watermark.get():
                watermark_color = self.color_names.get(self.watermark_color.get(), "默认颜色")
                self.log(f"🖼️ 封面水印已启用: {watermark_color}, 透明度:{self.watermark_opacity.get():.2f}, 位置:{self.watermark_position.get()}")

            # 清空目标文件夹
            self.log("🗑️ 清空处理后视频目录和封面目录...")

            # 使用单线程清空文件夹以避免序列化问题
            for folder in [processed_videos_dir, processed_covers_dir]:
                if os.path.exists(folder):
                    for file in os.listdir(folder):
                        file_path = os.path.join(folder, file)
                        try:
                            if os.path.isfile(file_path):
                                os.remove(file_path)
                        except Exception as e:
                            self.log(f"⚠️ 删除文件失败: {file} - {str(e)}")

            # 使用找到的文件列表
            video_files = found_files

            # 获取视频处理参数
            min_duration = float(self.video_min_duration.get())  # 秒
            max_duration = float(self.video_max_duration.get())  # 秒

            # 创建进度条
            total_videos = len(video_files)

            # 更新UI上的总数
            self.update_progress_ui(0, total_videos, 0, 0)

            # 获取并发处理线程数，根据CPU核心数和内存优化
            import multiprocessing

            # 默认设置
            cpu_count = multiprocessing.cpu_count()
            user_threads = int(self.thread_num.get())

            try:
                # 尝试导入psutil - 如果安装了此库，可以获得更智能的线程分配
                import psutil

                try:
                    # 获取系统信息
                    mem = psutil.virtual_memory()
                    total_mem_gb = mem.total / (1024**3)  # 转换为GB

                    # 根据系统性能决定线程数
                    if total_mem_gb < 4:  # 低内存系统
                        suggested_workers = max(1, cpu_count // 2)
                    elif total_mem_gb < 8:  # 中等内存系统
                        suggested_workers = max(2, cpu_count - 2)
                    else:  # 高内存系统
                        suggested_workers = cpu_count

                    # 获取用户设置的线程数，但不超过建议值
                    max_workers = min(total_videos, min(user_threads, suggested_workers))

                    self.log(f"🧮 系统信息: CPU {cpu_count}核, 内存 {total_mem_gb:.1f}GB")
                    self.log(f"🧵 使用 {max_workers} 个线程处理视频 (推荐值: {suggested_workers})")
                except Exception as e:
                    # 捕获psutil使用时可能出现的任何错误
                    self.log(f"⚠️ 使用psutil获取系统信息时出错: {str(e)}")
                    # 使用备选方案
                    max_workers = min(total_videos, min(user_threads, cpu_count))
                    self.log(f"🧵 使用备选方案: {max_workers} 个线程处理视频 (用户设置: {user_threads}, CPU: {cpu_count}核)")
            except ImportError:
                # 如果无法导入psutil，使用简单的线程数确定方法
                max_workers = min(total_videos, min(user_threads, cpu_count))
                self.log(f"🧵 使用 {max_workers} 个线程处理视频 (CPU: {cpu_count}核)")
            except Exception as e:
                # 捕获任何其他异常
                self.log(f"⚠️ 确定线程数时出错: {str(e)}")
                # 使用保守的设置
                max_workers = min(total_videos, min(2, cpu_count))
                self.log(f"🧵 使用保守设置: {max_workers} 个线程处理视频")

            # 创建全局锁，用于同步处理结果
            self.processing_lock = threading.Lock()

            # 创建已处理文件记录，避免重复处理
            processed_files = set()
            processed_count_lock = threading.Lock()

            # 进度更新函数
            def update_progress():
                with self.processing_lock:
                    progress = (self.total_processed / total_videos) * 100
                    message = f"🔄 预处理进度: {self.total_processed}/{total_videos} ({progress:.1f}%) | 成功: {self.success_count}, 失败: {self.failed_count}"
                    self.log(message)
                    # 更新UI上的进度
                    self.update_progress_ui(self.total_processed, total_videos, self.success_count, self.failed_count)

            # 创建任务队列，有助于更均匀地分配任务
            from queue import Queue
            task_queue = Queue()
            for video_file in video_files:
                task_queue.put(video_file)

            # 处理单个文件的函数
            def process_file_worker():
                while not task_queue.empty() and self.is_processing:
                    try:
                        # 从队列获取任务
                        video_file = task_queue.get(block=False)
                        if not video_file:
                            continue

                        # 检查是否已处理
                        with processed_count_lock:
                            if video_file in processed_files:
                                task_queue.task_done()
                                continue
                            processed_files.add(video_file)

                        result = self.process_video_file(
                            video_file,
                            video_dir,
                            processed_videos_dir,
                            processed_covers_dir,
                            min_duration,
                            max_duration
                        )

                        # 更新统计信息
                        with self.processing_lock:
                            self.total_processed += 1
                            if result.get('success'):
                                self.success_count += 1
                                # 仅在控制台显示成功消息，不在GUI中显示每个文件
                                print(result.get('message', ''))
                            else:
                                self.failed_count += 1
                                # 显示失败消息在GUI中
                                self.log(f"❌ {result.get('message', '处理失败')}")

                        # 每处理1个文件更新一次进度UI
                        self.update_progress_ui(self.total_processed, total_videos, self.success_count, self.failed_count)

                        # 每处理10个文件更新一次日志进度
                        if self.total_processed % 10 == 0 or self.total_processed == total_videos:
                            update_progress()

                        # 标记任务完成
                        task_queue.task_done()
                    except Exception as e:
                        print(f"处理任务异常: {str(e)}")

            # 创建并启动工作线程
            workers = []
            for _ in range(max_workers):
                thread = threading.Thread(target=process_file_worker)
                thread.daemon = True
                thread.start()
                workers.append(thread)

            # 等待任务完成
            try:
                # 主线程定期更新进度并检查是否应该停止
                while self.is_processing and any(thread.is_alive() for thread in workers) and not task_queue.empty():
                    # 每2秒更新一次进度
                    update_progress()
                    # 等待一小段时间
                    time.sleep(2)

                # 如果处理被停止，确保任务队列被清空
                if not self.is_processing:
                    with task_queue.mutex:
                        task_queue.queue.clear()
                    self.log("⏹️ 预处理已手动停止")
                else:
                    # 等待所有工作线程完成
                    for thread in workers:
                        thread.join(timeout=1.0)

                    # 最终更新进度
                    update_progress()
                    self.log(f"\n✅ 视频预处理完成!")
                    self.log(f"📊 总计: {total_videos}, 成功: {self.success_count}, 失败: {self.failed_count}")
            except Exception as e:
                self.log(f"监控任务进度时发生错误: {str(e)}")

        except Exception as e:
            self.log(f"❌ 预处理任务失败: {str(e)}")
            self.log(traceback.format_exc())

        finally:
            self.is_processing = False
            # 尝试释放资源
            import gc
            gc.collect()

    def start_preprocess(self):
        """开始预处理视频"""
        if self.is_processing:
            self.log("⚠️ 预处理任务正在运行中")
            return

        try:
            # 检查必要的目录设置
            if not self.video_dir.get():
                messagebox.showerror("错误", "请先设置待处理视频目录！")
                return
            if not self.processed_videos_dir.get():
                messagebox.showerror("错误", "请先设置处理后视频目录！")
                return
            if not self.processed_covers_dir.get():
                messagebox.showerror("错误", "请先设置处理后封面目录！")
                return

            # 启动预处理线程
            thread = threading.Thread(target=self.preprocess_task)
            thread.daemon = True
            thread.start()
        except Exception as e:
            self.log(f"❌ 启动预处理失败: {str(e)}")
            self.is_processing = False

    def schedule_auto_save(self):
        """计划自动保存配置"""
        self._auto_saving = True  # 标记为自动保存
        self.save_config()  # 保存配置
        self._auto_saving = False  # 重置标记
        # 计划下一次保存
        self.root.after(self.auto_save_interval, self.schedule_auto_save)

    def on_close(self):
        """窗口关闭时的处理"""
        self.save_config()  # 保存配置
        self.root.destroy()  # 关闭窗口

    def save_config(self):
        """保存配置到文件"""
        try:
            config_dir = os.path.dirname(self.config_file)
            # 确保配置文件目录存在
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)

            with open(self.config_file, "w", encoding="utf-8") as f:
                for var in self.tracked_vars:
                    # 获取变量名
                    name = str(var).split(".")[-1]
                    value = var.get()
                    f.write(f"{name}={value}\n")

            # 减少日志消息频率，只在手动保存时显示
            if not hasattr(self, '_auto_saving') or not self._auto_saving:
                self.log(f"✅ 配置已保存到: {self.config_file}")
        except Exception as e:
            self.log(f"⚠️ 保存配置失败: {str(e)}")

    def load_config(self):
        """从文件加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, "r", encoding="utf-8") as f:
                    config = {}
                    for line in f:
                        if "=" in line:
                            key, value = line.strip().split("=", 1)
                            config[key] = value

                # 应用配置到变量
                for var in self.tracked_vars:
                    name = str(var).split(".")[-1]
                    if name in config:
                        value = config[name]

                        # 根据变量类型设置值
                        if isinstance(var, tk.BooleanVar):
                            var.set(value.lower() == "true")
                        elif isinstance(var, tk.IntVar):
                            try:
                                var.set(int(value))
                            except ValueError:
                                pass
                        else:
                            var.set(value)

                # 特殊处理字体大小
                if 'font_size_var' in config:
                    try:
                        self.default_font_size = int(config['font_size_var'])
                        self.font_size_var.set(self.default_font_size)
                        self.configure_fonts()
                    except:
                        pass

                # 更新所有预览元素 - 修复错误的after调用
                self.root.after(100, self._update_all_previews)

                self.log("✅ 已加载保存的配置")
        except Exception as e:
            self.log(f"⚠️ 加载配置失败: {str(e)}")

    def _update_all_previews(self):
        """更新所有预览元素"""
        try:
            # 更新颜色预览
            if hasattr(self, 'color_preview_top'):
                self._update_color_preview(self.color_preview_top, self.cover_color_top.get())
            if hasattr(self, 'color_preview_bottom'):
                self._update_color_preview(self.color_preview_bottom, self.cover_color_bottom.get())
            if hasattr(self, 'watermark_color_preview'):
                self._update_color_preview(self.watermark_color_preview, self.watermark_color.get())

            # 更新字体大小预览
            if hasattr(self, 'font_preview_canvas'):
                self._update_font_size_preview()

            self.log("预览元素已更新")
        except Exception as e:
            print(f"更新预览元素失败: {str(e)}")

    def _update_color_preview(self, canvas, color_hex):
        """更新颜色预览框"""
        if canvas and color_hex:
            # 清除旧内容
            canvas.delete("all")
            # 绘制填充色块
            canvas.create_rectangle(0, 0, 20, 20, fill=color_hex, outline="#999999")
            # 刷新显示
            canvas.update_idletasks()

    def _update_font_size_preview(self):
        """更新字体大小预览"""
        try:
            if hasattr(self, 'font_preview_canvas'):
                # 获取当前字体大小
                font_size = self.cover_size.get()

                # 清除画布
                self.font_preview_canvas.delete("all")

                # 计算预览文本 - 使用更简洁的文本
                preview_text = "Aa中文"

                # 获取画布尺寸
                canvas_width = 180
                canvas_height = 60

                # 绘制背景
                self.font_preview_canvas.create_rectangle(0, 0, canvas_width, canvas_height,
                                                      fill="#F8F8F8", outline="#E0E0E0")

                # 使用Tkinter绘制文本 - 直接使用适当大小
                display_font_size = max(10, int(font_size/2.5))
                self.font_preview_canvas.create_text(canvas_width/2, canvas_height/2,
                                               text=preview_text,
                                               font=("微软雅黑", display_font_size),
                                               fill="black", anchor="center")

                # 显示实际字体大小
                self.font_preview_canvas.create_text(canvas_width-10, canvas_height-5,
                                               text=f"{font_size}px",
                                               font=("Arial", 9), fill="#666666", anchor="se")

                # 用很小的字体显示说明
                self.font_preview_canvas.create_text(canvas_width/2, 5,
                                               text="封面字体大小",
                                               font=("微软雅黑", 7), fill="#999999", anchor="n")
        except Exception as e:
            print(f"预览更新失败: {str(e)}")

    # 添加状态信息更新方法
    def update_status_info(self):
        """更新状态信息显示"""
        try:
            # 收集当前配置信息
            info_parts = []

            # 视频设置信息
            info_parts.append(f"视频条件: {self.video_min_duration.get()}-{self.video_max_duration.get()}秒, "
                            f"比例: {self.video_ratio.get()}, "
                            f"线程数: {self.thread_num.get()}")

            # 封面文字信息
            top_text = self.cover_text_top.get() or "[自动]"
            bottom_text = self.cover_text_bottom.get() or "[自动]"
            top_color = self.color_names.get(self.cover_color_top.get(), "默认")
            bottom_color = self.color_names.get(self.cover_color_bottom.get(), "默认")

            info_parts.append(f"上方文字: {top_text} ({top_color}), "
                            f"下方文字: {bottom_text} ({bottom_color}), "
                            f"字体大小: {self.cover_size.get()}px")

            # 功能开关状态
            dedup = "开启" if self.enable_deduplication.get() else "关闭"
            auto_filename = "开启" if self.auto_use_filename.get() else "关闭"
            watermark = "开启" if self.enable_watermark.get() else "关闭"

            # 水印信息
            if self.enable_watermark.get():
                watermark_color = self.color_names.get(self.watermark_color.get(), "默认")
                watermark_info = f"封面水印: {watermark} ({watermark_color}, 透明度:{self.watermark_opacity.get():.2f})"
            else:
                watermark_info = f"封面水印: {watermark}"

            info_parts.append(f"{watermark_info}, 视频去重: {dedup}, "
                            f"自动用文件名: {auto_filename}, "
                            f"界面字体: {self.default_font_size}px")

            # 组合并更新显示
            status_text = " | ".join(info_parts)
            self.status_text.set(status_text)
        except Exception as e:
            print(f"更新状态信息失败: {str(e)}")

    # 修复变量追踪的lambda函数
    def _create_trace_callback(self):
        return lambda *args: self.update_status_info()

    def _add_watermark_to_cover(self, cover_path):
        """为封面添加纯色水印"""
        if not self.enable_watermark.get():
            return True

        try:
            # 打开封面图片
            cover_img = Image.open(cover_path)
            width, height = cover_img.size

            # 创建一个相同大小的纯色图层
            color = self.watermark_color.get()
            opacity = float(self.watermark_opacity.get())

            # 将16进制颜色转换为RGB
            r = int(color[1:3], 16)
            g = int(color[3:5], 16)
            b = int(color[5:7], 16)

            # 创建一个带有Alpha通道的图层
            watermark_layer = Image.new('RGBA', (width, height), (r, g, b, int(opacity * 255)))

            # 根据位置设置水印大小和位置
            position = self.watermark_position.get()

            if position == "全屏":
                # 全屏水印
                pass  # 默认已经是全屏
            else:
                # 计算水印尺寸（屏幕的1/4）
                wm_width = width // 4
                wm_height = height // 4

                # 创建较小的水印
                watermark_layer = Image.new('RGBA', (wm_width, wm_height), (r, g, b, int(opacity * 255)))

                # 创建一个透明的全屏图层
                full_layer = Image.new('RGBA', (width, height), (0, 0, 0, 0))

                # 根据位置设置坐标
                if position == "左上角":
                    pos = (0, 0)
                elif position == "右上角":
                    pos = (width - wm_width, 0)
                elif position == "左下角":
                    pos = (0, height - wm_height)
                elif position == "右下角":
                    pos = (width - wm_width, height - wm_height)
                elif position == "中心":
                    pos = ((width - wm_width) // 2, (height - wm_height) // 2)

                # 将小水印放到指定位置
                full_layer.paste(watermark_layer, pos)
                watermark_layer = full_layer

            # 转换原图为RGBA模式
            if cover_img.mode != 'RGBA':
                cover_img = cover_img.convert('RGBA')

            # 合成图像
            result = Image.alpha_composite(cover_img, watermark_layer)

            # 转换回RGB并保存
            result_rgb = result.convert('RGB')
            result_rgb.save(cover_path, quality=95)

            return True
        except Exception as e:
            print(f"添加封面水印失败: {str(e)}")
            return False

    def _add_watermark_to_video(self, input_path, output_path):
        """复制视频文件，不再添加水印"""
        # 直接复制视频文件
        shutil.copy2(input_path, output_path)
        return True


# 主程序入口已移至文件开头，此文件已弃用