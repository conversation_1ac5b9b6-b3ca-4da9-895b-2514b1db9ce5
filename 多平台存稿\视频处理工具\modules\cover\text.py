"""
封面文字处理模块 - 处理封面文字的添加和编辑
"""

import os
from typing import Dict, Tuple, Optional, Callable, Union
from PIL import Image, ImageDraw, ImageFont

from ..common.utils import smart_split_filename

def add_text_to_cover(processor,
                     cover_path: str, 
                     base_filename: str,
                     top_text: str = "",
                     bottom_text: str = "",
                     top_color: str = "#FFFFFF",
                     bottom_color: str = "#FFFFFF",
                     font_size: int = 60,
                     auto_use_filename: bool = True,
                     cover_resolution: Tuple[int, int] = None) -> bool:
    """
    添加文字到封面图片
    
    Args:
        processor: 封面处理器实例
        cover_path: 封面图片路径
        base_filename: 基础文件名（用于自动生成文字）
        top_text: 上方文字
        bottom_text: 下方文字
        top_color: 上方文字颜色（十六进制）
        bottom_color: 下方文字颜色（十六进制）
        font_size: 字体大小
        auto_use_filename: 是否自动使用文件名
        cover_resolution: 封面分辨率，如果指定则调整大小
        
    Returns:
        bool: 是否成功添加文字
    """
    try:
        # 打开图片
        cover_img = Image.open(cover_path)
        
        # 如果指定了分辨率，调整图片大小
        if cover_resolution:
            cover_img = cover_img.resize(cover_resolution, Image.LANCZOS)
            
        img_width, img_height = cover_img.size
        
        # 准备封面文字
        final_top_text = top_text
        final_bottom_text = bottom_text
        
        # 根据设置和用户输入决定如何处理文字
        if auto_use_filename:
            # 预清理文件名，移除特殊字符和随机后缀
            clean_filename = base_filename
            
            # 如果开启了自动使用文件名功能
            if not top_text and not bottom_text:
                # 如果用户没有填写任何文字，则智能拆分文件名为上下两部分
                if len(clean_filename) > 0:
                    final_top_text, final_bottom_text = smart_split_filename(clean_filename)
            elif not top_text:
                # 如果只有左上角文字为空，使用文件名前半部分
                first_part, _ = smart_split_filename(clean_filename)
                final_top_text = first_part
            elif not bottom_text:
                # 如果只有右下角文字为空，使用文件名后半部分
                if len(clean_filename) > 0:
                    _, second_part = smart_split_filename(clean_filename)
                    final_bottom_text = second_part
        elif not top_text and not bottom_text:
            # 当用户未填写左上和右下文字且未开启自动使用文件名时，智能拆分文件名
            # 预清理文件名，移除特殊字符和随机后缀
            clean_filename = base_filename
            if len(clean_filename) > 0:
                final_top_text, final_bottom_text = smart_split_filename(clean_filename)
        
        # 保存原始的设定字体大小
        original_font_size = font_size
        
        # 绘制封面文字
        if final_top_text or final_bottom_text:
            draw = ImageDraw.Draw(cover_img)
            
            # 加载字体的函数
            def load_font(size):
                try:
                    # 尝试加载微软雅黑粗体字体
                    try:
                        return ImageFont.truetype("msyhbd.ttc", size)
                    except:
                        try:
                            return ImageFont.truetype("msyh.ttc", size)
                        except:
                            try:
                                return ImageFont.truetype("arial.ttf", size)
                            except:
                                return ImageFont.load_default()
                except:
                    return ImageFont.load_default()
                    
            # 检查文字是否超出边界，如果超出，则减小字体大小
            def adjust_font_size(text, max_width, initial_size, min_size=20):
                # 如果文本为空，直接返回
                if not text:
                    font = load_font(initial_size)
                    return font, 0, 0, ""
                
                # 初始字体大小
                font_size = initial_size
                font = load_font(font_size)
                
                # 获取文字大小
                bbox = draw.textbbox((0, 0), text, font=font)
                text_width = bbox[2] - bbox[0]
                
                # 如果文字宽度超过最大宽度，逐渐减小字体大小
                while text_width > max_width and font_size > min_size:
                    font_size -= 2  # 每次减小2个点
                    font = load_font(font_size)
                    bbox = draw.textbbox((0, 0), text, font=font)
                    text_width = bbox[2] - bbox[0]
                
                # 如果字体已经最小，但文本仍然太长，进行截断
                if text_width > max_width:
                    # 智能截断，尝试保留语义
                    # 中文字符优先保留完整词组
                    has_chinese = any('\u4e00' <= char <= '\u9fff' for char in text)
                    
                    if has_chinese:
                        # 中文文本按字符截断比较合理
                        ratio = max_width / text_width
                        keep_chars = max(int(len(text) * ratio * 0.9), 6)  # 至少保留6个字符
                        
                        if keep_chars < len(text):
                            # 确保不会在中文字符中间截断
                            if keep_chars < len(text) - 3:
                                text = text[:keep_chars] + "..."
                            else:
                                # 如果只剩下几个字符，干脆全部显示
                                pass
                    else:
                        # 英文文本尝试在空格处截断
                        words = text.split()
                        truncated = []
                        current_width = 0
                        
                        for word in words:
                            word_bbox = draw.textbbox((0, 0), word + " ", font=font)
                            word_width = word_bbox[2] - word_bbox[0]
                            
                            if current_width + word_width <= max_width * 0.9:
                                truncated.append(word)
                                current_width += word_width
                            else:
                                break
                        
                        if truncated and len(truncated) < len(words):
                            text = " ".join(truncated) + "..."
                        elif len(text) > 15:  # 如果是一个很长的单词
                            ratio = max_width / text_width
                            keep_chars = max(int(len(text) * ratio * 0.9), 12)
                            text = text[:keep_chars] + "..."
                    
                    # 重新测量截断后的宽度
                    bbox = draw.textbbox((0, 0), text, font=font)
                    text_width = bbox[2] - bbox[0]
                
                # 最后一次检查，极端情况处理
                if text_width > max_width and len(text) > 10:
                    # 强制截断
                    text = text[:7] + "..."
                    bbox = draw.textbbox((0, 0), text, font=font)
                    text_width = bbox[2] - bbox[0]
                    
                return font, text_width, bbox[3] - bbox[1], text  # 返回字体对象、文字宽度、高度和可能被截断的文本
            
            # 留出边距
            margin = 20
            max_text_width = img_width - (margin * 2)
            
            # 绘制左上角文字
            if final_top_text:
                text_color = processor.color_map.get(top_color, (255, 255, 255))
                
                # 获取最佳字体大小和文字尺寸，以及可能被截断的文本
                top_font, text_width, text_height, truncated_top_text = adjust_font_size(final_top_text, max_text_width, original_font_size)
                
                # 左上角位置
                text_x = margin
                text_y = margin
                
                # 绘制文字阴影
                shadow_offset = 2
                draw.text((text_x + shadow_offset, text_y + shadow_offset), 
                         truncated_top_text, font=top_font, fill=(0, 0, 0))
                
                # 绘制文字
                draw.text((text_x, text_y), truncated_top_text, font=top_font, fill=text_color)
            
            # 绘制右下角文字
            if final_bottom_text:
                text_color = processor.color_map.get(bottom_color, (255, 255, 255))
                
                # 获取最佳字体大小和文字尺寸，以及可能被截断的文本
                bottom_font, text_width, text_height, truncated_bottom_text = adjust_font_size(final_bottom_text, max_text_width, original_font_size)
                
                # 右下角位置
                text_x = img_width - text_width - margin
                text_y = img_height - text_height - margin
                
                # 确保文字不会超出图片边界
                text_x = max(margin, text_x)
                text_y = max(margin, text_y)
                
                # 绘制文字阴影
                shadow_offset = 2
                draw.text((text_x + shadow_offset, text_y + shadow_offset), 
                         truncated_bottom_text, font=bottom_font, fill=(0, 0, 0))
                
                # 绘制文字
                draw.text((text_x, text_y), truncated_bottom_text, font=bottom_font, fill=text_color)
        
        # 保存封面
        cover_img.save(cover_path, quality=95)
        return True
    except Exception as e:
        processor.logger(f"添加封面文字失败: {str(e)}")
        return False
