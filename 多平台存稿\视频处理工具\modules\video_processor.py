"""
视频处理模块 - 处理视频的核心功能
"""

import os
import re
import uuid
import json
import shutil
import hashlib
import subprocess
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union, Callable, Set
from PIL import Image

from .cover_processor import CoverProcessor
from .utils import clean_filename

class VideoProcessor:
    """视频处理类，负责视频的处理和转换"""

    def __init__(self, logger: Callable = print):
        """
        初始化视频处理器

        Args:
            logger: 日志记录函数
        """
        self.logger = logger
        self.cover_processor = CoverProcessor(logger)
        # 存储已处理视频的哈希值，用于去重
        self.video_hashes = {}
        # 文件名过滤关键词
        self.filename_filters: Set[str] = set()

    def load_filters_from_string(self, filters_str: str) -> None:
        """
        从字符串加载过滤关键词

        Args:
            filters_str: 过滤关键词字符串，用逗号分隔
        """
        if not filters_str:
            return

        # 清空现有过滤关键词
        self.filename_filters.clear()

        # 添加新的过滤关键词
        for keyword in filters_str.split(','):
            keyword = keyword.strip()
            if keyword:
                self.filename_filters.add(keyword)

        self.logger(f"已加载 {len(self.filename_filters)} 个文件名过滤关键词")

    def get_filters_as_string(self) -> str:
        """
        将过滤关键词转换为字符串

        Returns:
            str: 过滤关键词字符串，用逗号分隔
        """
        return ','.join(sorted(self.filename_filters))

    def compute_video_hash(self, video_path: str, sample_frames: int = 10) -> str:
        """
        计算视频的哈希值，用于去重

        Args:
            video_path: 视频文件路径
            sample_frames: 采样帧数，默认为10

        Returns:
            str: 视频的哈希值
        """
        try:
            # 使用ffprobe获取视频时长
            cmd = [
                'ffprobe',
                '-v', 'error',
                '-select_streams', 'v:0',
                '-show_entries', 'stream=duration',
                '-of', 'json',
                video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                self.logger(f"无法获取视频时长: {result.stderr}")
                return hashlib.md5(os.path.basename(video_path).encode()).hexdigest()

            video_info = json.loads(result.stdout)
            duration = float(video_info.get('streams', [{}])[0].get('duration', 0))

            if duration <= 0:
                self.logger(f"视频时长无效: {duration}")
                return hashlib.md5(os.path.basename(video_path).encode()).hexdigest()

            # 计算采样时间点
            sample_times = [duration * i / (sample_frames + 1) for i in range(1, sample_frames + 1)]

            # 提取帧并计算哈希
            frame_hashes = []
            for time_point in sample_times:
                # 使用ffmpeg提取帧
                frame_cmd = [
                    'ffmpeg',
                    '-ss', str(time_point),
                    '-i', video_path,
                    '-vframes', '1',
                    '-f', 'image2pipe',
                    '-pix_fmt', 'rgb24',
                    '-vcodec', 'rawvideo',
                    '-'
                ]

                frame_result = subprocess.run(frame_cmd, capture_output=True)
                if frame_result.returncode != 0:
                    continue

                # 将原始图像数据转换为numpy数组
                try:
                    # 使用ffprobe获取视频帧尺寸
                    size_cmd = [
                        'ffprobe',
                        '-v', 'error',
                        '-select_streams', 'v:0',
                        '-show_entries', 'stream=width,height',
                        '-of', 'json',
                        video_path
                    ]

                    size_result = subprocess.run(size_cmd, capture_output=True, text=True)
                    if size_result.returncode != 0:
                        continue

                    size_info = json.loads(size_result.stdout)
                    width = int(size_info.get('streams', [{}])[0].get('width', 0))
                    height = int(size_info.get('streams', [{}])[0].get('height', 0))

                    if width <= 0 or height <= 0:
                        continue

                    # 将原始图像数据转换为numpy数组
                    frame_data = np.frombuffer(frame_result.stdout, dtype=np.uint8)
                    if len(frame_data) != width * height * 3:
                        # 调整大小以匹配预期
                        continue

                    # 重塑为图像
                    frame = frame_data.reshape((height, width, 3))

                    # 缩小图像以减少计算量
                    small_frame = frame[::8, ::8]

                    # 计算帧的哈希值
                    frame_hash = hashlib.md5(small_frame.tobytes()).hexdigest()
                    frame_hashes.append(frame_hash)
                except Exception as e:
                    self.logger(f"计算帧哈希时出错: {str(e)}")
                    continue

            # 如果没有成功提取任何帧，使用文件名作为哈希
            if not frame_hashes:
                return hashlib.md5(os.path.basename(video_path).encode()).hexdigest()

            # 组合所有帧哈希
            combined_hash = hashlib.md5(''.join(frame_hashes).encode()).hexdigest()
            return combined_hash

        except Exception as e:
            self.logger(f"计算视频哈希时出错: {str(e)}")
            # 如果出错，使用文件名作为哈希
            return hashlib.md5(os.path.basename(video_path).encode()).hexdigest()

    def add_filename_filter(self, keyword: str) -> None:
        """
        添加文件名过滤关键词

        Args:
            keyword: 要过滤的关键词
        """
        if keyword and keyword.strip():
            self.filename_filters.add(keyword.strip())
            self.logger(f"已添加文件名过滤关键词: {keyword}")

    def remove_filename_filter(self, keyword: str) -> None:
        """
        移除文件名过滤关键词

        Args:
            keyword: 要移除的关键词
        """
        if keyword in self.filename_filters:
            self.filename_filters.remove(keyword)
            self.logger(f"已移除文件名过滤关键词: {keyword}")

    def clear_filename_filters(self) -> None:
        """清空所有文件名过滤关键词"""
        self.filename_filters.clear()
        self.logger("已清空所有文件名过滤关键词")

    def should_filter_file(self, filename: str) -> Tuple[bool, str]:
        """
        检查文件名是否应该被过滤

        Args:
            filename: 文件名

        Returns:
            Tuple[bool, str]: (是否应过滤, 匹配的关键词)
        """
        if not self.filename_filters:
            return False, ""

        # 转换为小写进行不区分大小写的比较
        lower_filename = filename.lower()

        for keyword in self.filename_filters:
            if keyword.lower() in lower_filename:
                return True, keyword

        return False, ""

    def is_duplicate_video(self, video_path: str, threshold: float = 0.9, sample_frames: int = 10) -> Tuple[bool, str]:
        """
        检查视频是否是重复的

        Args:
            video_path: 视频文件路径
            threshold: 相似度阈值，默认为0.9
            sample_frames: 采样帧数，默认为10

        Returns:
            Tuple[bool, str]: (是否重复, 重复视频的路径)
        """
        try:
            # 计算当前视频的哈希值
            current_hash = self.compute_video_hash(video_path, sample_frames=sample_frames)

            # 检查是否与已处理的视频重复
            for path, hash_value in self.video_hashes.items():
                if hash_value == current_hash:
                    return True, path

            # 如果没有重复，添加到哈希列表
            self.video_hashes[video_path] = current_hash
            return False, ""

        except Exception as e:
            self.logger(f"检查视频重复时出错: {str(e)}")
            return False, ""

    def process_video(self,
                     video_file: str,
                     video_dir: str,
                     processed_videos_dir: str,
                     processed_covers_dir: str,
                     min_duration: float,
                     max_duration: float,
                     target_ratio: str,
                     enable_deduplication: bool = True,
                     dedup_settings: Dict[str, Any] = None,
                     cover_settings: Dict[str, Any] = None,
                     watermark_settings: Dict[str, Any] = None,
                     output_format: str = None,
                     video_codec: str = None,
                     cover_resolution: Tuple[int, int] = None,
                     gpu_settings: Dict[str, Any] = None,
                     resource_settings: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理单个视频文件

        Args:
            video_file: 视频文件名
            video_dir: 视频源目录
            processed_videos_dir: 处理后视频目录
            processed_covers_dir: 处理后封面目录
            min_duration: 最小视频时长（秒）
            max_duration: 最大视频时长（秒）
            target_ratio: 目标视频比例（如"16:9"）
            enable_deduplication: 是否启用视频去重
            dedup_settings: 视频去重设置，包含method（去重方式）和sample_frames（采样帧数）等参数
            cover_settings: 封面设置
            watermark_settings: 水印设置
            output_format: 输出视频格式（如"mp4"、"mov"等），None表示保持原格式
            video_codec: 视频编码器（如"h264"、"h265"、"vp9"等），None表示使用默认编码器
            cover_resolution: 封面分辨率，如(1280, 720)
            gpu_settings: GPU加速设置，包含enable和device等参数
            resource_settings: 智能资源分配设置，包含enable和memory_limit等参数

        Returns:
            Dict: 处理结果，包含success、message等信息
        """
        try:
            # 视频文件完整路径
            video_path = os.path.join(video_dir, video_file)

            # 检查视频是否存在
            if not os.path.exists(video_path):
                return {
                    'success': False,
                    'message': f"视频文件不存在: {video_path}"
                }

            # 检查文件名是否应该被过滤
            should_filter, filter_keyword = self.should_filter_file(video_file)
            if should_filter:
                return {
                    'success': False,
                    'message': f"视频 {video_file} 包含过滤关键词 '{filter_keyword}'，已跳过"
                }

            # 检查GPU加速设置
            use_gpu = False
            gpu_device = "auto"
            if gpu_settings and gpu_settings.get('enable', False):
                use_gpu = True
                gpu_device = gpu_settings.get('device', 'auto')
                self.logger(f"GPU加速已启用，设备: {gpu_device}")

            # 检查资源限制设置
            memory_limit = 0  # 0表示不限制
            if resource_settings and resource_settings.get('enable', False):
                memory_limit = resource_settings.get('memory_limit', 0)
                if memory_limit > 0:
                    self.logger(f"内存限制已设置: {memory_limit}MB")

            # 处理文件名（保持原始文件名）
            file_name = os.path.splitext(video_file)[0]  # 文件名（不含扩展名）
            file_ext = os.path.splitext(video_file)[1]   # 扩展名

            # 如果指定了输出格式，更改扩展名
            if output_format:
                if not output_format.startswith('.'):
                    output_format = '.' + output_format
                file_ext = output_format

            # 清理文件名
            file_name = clean_filename(file_name)

            # 如果开启了视频去重，检查视频内容是否重复
            if enable_deduplication:
                # 获取去重设置
                dedup_method = "content"  # 默认使用内容去重
                sample_frames = 10  # 默认采样10帧

                if dedup_settings:
                    dedup_method = dedup_settings.get('method', dedup_method)
                    sample_frames = dedup_settings.get('sample_frames', sample_frames)

                # 根据去重方式进行检查
                if dedup_method == "content":
                    # 基于视频内容的去重
                    self.logger(f"使用基于内容的视频去重，采样帧数: {sample_frames}")
                    is_duplicate, duplicate_path = self.is_duplicate_video(video_path, sample_frames=sample_frames)
                    if is_duplicate:
                        self.logger(f"检测到重复视频: {video_path} 与 {duplicate_path} 内容相似")
                        return {
                            'success': False,
                            'message': f"视频 {video_file} 与已处理的视频内容重复，已跳过"
                        }
                else:
                    # 基于文件名的去重（简单模式）
                    self.logger(f"使用基于文件名的视频去重")
                    # 这里不需要做额外的检查，因为后面的代码已经会处理文件名重复的情况

                # 使用UUID确保唯一性，但不直接显示在文件名中
                short_uuid = str(uuid.uuid4())[:8]
                base_name = file_name[:30] if len(file_name) > 30 else file_name

                # 隐藏UUID到文件名末尾隐藏属性中，不改变可见文件名
                processed_name = base_name

                # 仅当检测到重名时才添加UUID
                test_path = os.path.join(processed_videos_dir, f"{processed_name}{file_ext}")
                if os.path.exists(test_path):
                    processed_name = f"{base_name}_{short_uuid}"

                truncated_name = f"{processed_name}{file_ext}"
            else:
                # 如果不开启去重，仍然确保文件名不会太长
                if len(file_name) > 30:
                    truncated_name = f"{file_name[:30]}{file_ext}"
                else:
                    truncated_name = f"{file_name}{file_ext}"

            # 设置输出路径
            output_path = os.path.join(processed_videos_dir, truncated_name)
            cover_name = f"{os.path.splitext(truncated_name)[0]}.jpg"
            cover_path = os.path.join(processed_covers_dir, cover_name)

            # 检查输出文件是否已存在 (在非去重模式下可能发生)
            if os.path.exists(output_path):
                # 生成新的文件名，但保持干净的命名方式
                base_name = os.path.splitext(truncated_name)[0]
                count = 1

                # 尝试添加序号直到找到可用的文件名
                while os.path.exists(output_path):
                    new_name = f"{base_name}_{count}"
                    truncated_name = f"{new_name}{file_ext}"
                    output_path = os.path.join(processed_videos_dir, truncated_name)
                    count += 1

                # 更新封面路径
                cover_name = f"{os.path.splitext(truncated_name)[0]}.jpg"
                cover_path = os.path.join(processed_covers_dir, cover_name)

            # 使用ffmpeg快速获取视频信息（比moviepy更快）
            try:
                # 优先使用ffprobe快速获取视频信息
                try:
                    # 使用ffprobe获取视频信息
                    cmd = [
                        'ffprobe',
                        '-v', 'error',
                        '-select_streams', 'v:0',
                        '-show_entries', 'stream=width,height,duration',
                        '-of', 'json',
                        video_path
                    ]

                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode == 0:
                        video_info = json.loads(result.stdout)
                        stream = video_info.get('streams', [{}])[0]

                        width = int(stream.get('width', 0))
                        height = int(stream.get('height', 0))
                        duration_seconds = float(stream.get('duration', 0))

                        if width > 0 and height > 0 and duration_seconds > 0:
                            # 检查视频时长
                            min_seconds = min_duration
                            max_seconds = max_duration

                            if duration_seconds < min_seconds:
                                return {
                                    'success': False,
                                    'message': f"视频 {video_file} 时长过短: {duration_seconds:.2f}秒 < {min_seconds}秒"
                                }

                            if duration_seconds > max_seconds:
                                return {
                                    'success': False,
                                    'message': f"视频 {video_file} 时长过长: {duration_seconds:.2f}秒 > {max_seconds}秒"
                                }

                            # 检查视频比例
                            aspect_ratio = width / height

                            if target_ratio == "16:9":
                                target_aspect = 16 / 9
                            elif target_ratio == "4:3":
                                target_aspect = 4 / 3
                            elif target_ratio == "1:1":
                                target_aspect = 1
                            elif target_ratio == "9:16":
                                target_aspect = 9 / 16
                            else:
                                target_aspect = 16 / 9  # 默认16:9

                            # 容差为0.1
                            if abs(aspect_ratio - target_aspect) > 0.1:
                                return {
                                    'success': False,
                                    'message': f"视频 {video_file} 比例不符合要求: {aspect_ratio:.2f}，期望: {target_aspect:.2f}"
                                }

                            # 提取封面（使用ffmpeg）
                            cover_time = min(duration_seconds - 0.5, 5.0) if duration_seconds > 5.0 else duration_seconds / 2
                            cover_cmd = ['ffmpeg', '-ss', str(cover_time), '-i', video_path]

                            # 如果启用了GPU加速，尝试使用GPU解码
                            if use_gpu and gpu_device != "auto":
                                # 检查是否是NVIDIA GPU (CUDA)
                                if gpu_device.startswith("cuda:"):
                                    # 使用NVIDIA GPU加速
                                    self.logger(f"使用NVIDIA GPU加速提取封面")
                                    # 添加GPU解码参数
                                    cover_cmd.extend(['-hwaccel', 'cuda'])
                                    # 如果指定了具体的GPU设备，添加设备参数
                                    device_index = gpu_device.split(':')[1]
                                    if device_index.isdigit():
                                        cover_cmd.extend(['-hwaccel_device', device_index])

                            # 添加输出参数
                            cover_cmd.extend(['-vframes', '1', '-q:v', '2', cover_path])

                            # 如果设置了内存限制，添加内存限制参数
                            if memory_limit > 0:
                                # 转换为MB
                                mem_limit_bytes = memory_limit * 1024 * 1024
                                cover_cmd.insert(1, "-max_memory")
                                cover_cmd.insert(2, str(mem_limit_bytes))

                            cover_result = subprocess.run(cover_cmd, capture_output=True)

                            # 检查封面是否成功提取
                            if cover_result.returncode == 0 and os.path.exists(cover_path):
                                # 如果指定了封面分辨率，调整大小
                                if cover_resolution:
                                    try:
                                        cover_img = Image.open(cover_path)
                                        cover_img = cover_img.resize(cover_resolution, Image.LANCZOS)
                                        cover_img.save(cover_path, quality=95)
                                    except Exception as e:
                                        self.logger(f"调整封面分辨率失败: {str(e)}")

                                # 处理封面文字
                                if cover_settings:
                                    self.cover_processor.add_text_to_cover(
                                        cover_path,
                                        os.path.splitext(video_file)[0],
                                        top_text=cover_settings.get('top_text', ''),
                                        bottom_text=cover_settings.get('bottom_text', ''),
                                        top_color=cover_settings.get('top_color', '#FFFFFF'),
                                        bottom_color=cover_settings.get('bottom_color', '#FFFFFF'),
                                        font_size=cover_settings.get('font_size', 60),
                                        auto_use_filename=cover_settings.get('auto_use_filename', True),
                                        cover_resolution=cover_resolution
                                    )

                                # 添加封面水印
                                if watermark_settings and watermark_settings.get('enable', False):
                                    self.cover_processor.add_watermark_to_cover(
                                        cover_path,
                                        watermark_color=watermark_settings.get('color', '#FFFFFF'),
                                        opacity=watermark_settings.get('opacity', 0.1),
                                        position=watermark_settings.get('position', '全屏'),
                                        quantity=watermark_settings.get('quantity', 1)
                                    )

                                # 处理视频文件 - 如果需要转换格式
                                if output_format and os.path.splitext(video_path)[1].lower() != output_format.lower():
                                    # 使用ffmpeg转换格式
                                    convert_cmd = ['ffmpeg', '-i', video_path]

                                    # 根据编码器设置和GPU加速选择编码方式
                                    if video_codec:
                                        # 使用用户指定的编码器
                                        self.logger(f"使用指定的编码器: {video_codec}")

                                        # 检查是否可以使用GPU加速
                                        if use_gpu and gpu_device != "auto" and gpu_device.startswith("cuda:"):
                                            # 根据编码器选择对应的NVIDIA硬件编码器
                                            if video_codec == "h264":
                                                encoder = "h264_nvenc"
                                            elif video_codec == "h265" or video_codec == "hevc":
                                                encoder = "hevc_nvenc"
                                            else:
                                                # 不支持的编码器，使用软件编码
                                                encoder = video_codec
                                                self.logger(f"编码器 {video_codec} 不支持NVIDIA硬件加速，使用软件编码")

                                            # 使用NVIDIA GPU加速
                                            if encoder.endswith("_nvenc"):
                                                self.logger(f"使用NVIDIA GPU加速转换视频格式，编码器: {encoder}")
                                                convert_cmd.extend([
                                                    '-c:v', encoder,     # 使用NVIDIA硬件编码
                                                    '-preset', 'fast',   # 使用快速预设
                                                    '-c:a', 'copy',      # 复制音频流
                                                    output_path
                                                ])
                                            else:
                                                # 使用软件编码
                                                convert_cmd.extend([
                                                    '-c:v', encoder,     # 使用指定的软件编码器
                                                    '-c:a', 'copy',      # 复制音频流
                                                    output_path
                                                ])
                                        else:
                                            # 不使用GPU，使用软件编码
                                            convert_cmd.extend([
                                                '-c:v', video_codec,  # 使用指定的软件编码器
                                                '-c:a', 'copy',       # 复制音频流
                                                output_path
                                            ])
                                    else:
                                        # 没有指定编码器，使用默认设置
                                        if use_gpu and gpu_device != "auto" and gpu_device.startswith("cuda:"):
                                            # 使用NVIDIA GPU加速
                                            self.logger(f"使用NVIDIA GPU加速转换视频格式")
                                            convert_cmd.extend([
                                                '-c:v', 'h264_nvenc',  # 使用NVIDIA硬件编码
                                                '-preset', 'fast',     # 使用快速预设
                                                '-c:a', 'copy',        # 复制音频流
                                                output_path
                                            ])
                                        else:
                                            # 不使用GPU，直接复制流
                                            convert_cmd.extend(['-c', 'copy', output_path])

                                    # 如果设置了内存限制，添加内存限制参数
                                    if memory_limit > 0:
                                        # 转换为MB
                                        mem_limit_bytes = memory_limit * 1024 * 1024
                                        convert_cmd.insert(1, "-max_memory")
                                        convert_cmd.insert(2, str(mem_limit_bytes))

                                    convert_result = subprocess.run(convert_cmd, capture_output=True)

                                    if convert_result.returncode != 0:
                                        # 如果转换失败，回退到直接复制
                                        self.logger(f"格式转换失败，直接复制文件: {str(convert_result.stderr)}")
                                        shutil.copy2(video_path, output_path)
                                else:
                                    # 直接复制视频文件
                                    shutil.copy2(video_path, output_path)

                                return {
                                    'success': True,
                                    'message': f"✅ 视频 {video_file} 处理成功，已保存为: {truncated_name}",
                                    'file': video_file
                                }

                            # 如果ffmpeg提取封面失败，回退到moviepy方法
                            raise Exception("ffmpeg提取封面失败，回退到moviepy")
                        else:
                            raise Exception("无法从ffprobe获取完整视频信息")
                    else:
                        raise Exception(f"ffprobe返回错误: {result.stderr}")

                except Exception as ffmpeg_err:
                    # 如果ffprobe失败，继续尝试moviepy
                    self.logger(f"ffprobe处理失败，回退到moviepy: {str(ffmpeg_err)}")

                # 使用moviepy作为备选方案
                from moviepy.video.io.VideoFileClip import VideoFileClip

                with VideoFileClip(video_path) as video:
                    duration_seconds = video.duration
                    width, height = video.size

                    # 检查视频时长
                    min_seconds = min_duration
                    max_seconds = max_duration

                    if duration_seconds < min_seconds:
                        return {
                            'success': False,
                            'message': f"视频 {video_file} 时长过短: {duration_seconds:.2f}秒 < {min_seconds}秒"
                        }

                    if duration_seconds > max_seconds:
                        return {
                            'success': False,
                            'message': f"视频 {video_file} 时长过长: {duration_seconds:.2f}秒 > {max_seconds}秒"
                        }

                    # 检查视频比例
                    aspect_ratio = width / height

                    if target_ratio == "16:9":
                        target_aspect = 16 / 9
                    elif target_ratio == "4:3":
                        target_aspect = 4 / 3
                    elif target_ratio == "1:1":
                        target_aspect = 1
                    elif target_ratio == "9:16":
                        target_aspect = 9 / 16
                    else:
                        target_aspect = 16 / 9  # 默认16:9

                    # 容差为0.1
                    if abs(aspect_ratio - target_aspect) > 0.1:
                        return {
                            'success': False,
                            'message': f"视频 {video_file} 比例不符合要求: {aspect_ratio:.2f}，期望: {target_aspect:.2f}"
                        }

                    # 提取封面
                    # 获取视频中间帧作为封面
                    cover_time = min(video.duration - 0.5, 5.0) if video.duration > 5.0 else video.duration / 2
                    frame = video.get_frame(cover_time)

                    # 保存封面
                    cover_img = Image.fromarray(frame)

                    # 如果指定了封面分辨率，调整大小
                    if cover_resolution:
                        cover_img = cover_img.resize(cover_resolution, Image.LANCZOS)

                    cover_img.save(cover_path, quality=95)

                    # 处理封面文字
                    if cover_settings:
                        self.cover_processor.add_text_to_cover(
                            cover_path,
                            os.path.splitext(video_file)[0],
                            top_text=cover_settings.get('top_text', ''),
                            bottom_text=cover_settings.get('bottom_text', ''),
                            top_color=cover_settings.get('top_color', '#FFFFFF'),
                            bottom_color=cover_settings.get('bottom_color', '#FFFFFF'),
                            font_size=cover_settings.get('font_size', 60),
                            auto_use_filename=cover_settings.get('auto_use_filename', True),
                            cover_resolution=cover_resolution
                        )

                    # 添加封面水印
                    if watermark_settings and watermark_settings.get('enable', False):
                        self.cover_processor.add_watermark_to_cover(
                            cover_path,
                            watermark_color=watermark_settings.get('color', '#FFFFFF'),
                            opacity=watermark_settings.get('opacity', 0.1),
                            position=watermark_settings.get('position', '全屏'),
                            quantity=watermark_settings.get('quantity', 1)
                        )

            except Exception as e:
                return {
                    'success': False,
                    'message': f"处理视频 {video_file} 时出错: {str(e)}"
                }

            # 处理视频文件 - 如果需要转换格式
            if output_format and os.path.splitext(video_path)[1].lower() != output_format.lower():
                try:
                    # 使用ffmpeg转换格式
                    convert_cmd = ['ffmpeg', '-i', video_path]

                    # 根据编码器设置和GPU加速选择编码方式
                    if video_codec:
                        # 使用用户指定的编码器
                        self.logger(f"使用指定的编码器: {video_codec}")

                        # 检查是否可以使用GPU加速
                        if use_gpu and gpu_device != "auto" and gpu_device.startswith("cuda:"):
                            # 根据编码器选择对应的NVIDIA硬件编码器
                            if video_codec == "h264":
                                encoder = "h264_nvenc"
                            elif video_codec == "h265" or video_codec == "hevc":
                                encoder = "hevc_nvenc"
                            else:
                                # 不支持的编码器，使用软件编码
                                encoder = video_codec
                                self.logger(f"编码器 {video_codec} 不支持NVIDIA硬件加速，使用软件编码")

                            # 使用NVIDIA GPU加速
                            if encoder.endswith("_nvenc"):
                                self.logger(f"使用NVIDIA GPU加速转换视频格式，编码器: {encoder}")
                                convert_cmd.extend([
                                    '-c:v', encoder,     # 使用NVIDIA硬件编码
                                    '-preset', 'fast',   # 使用快速预设
                                    '-c:a', 'copy',      # 复制音频流
                                    output_path
                                ])
                            else:
                                # 使用软件编码
                                convert_cmd.extend([
                                    '-c:v', encoder,     # 使用指定的软件编码器
                                    '-c:a', 'copy',      # 复制音频流
                                    output_path
                                ])
                        else:
                            # 不使用GPU，使用软件编码
                            convert_cmd.extend([
                                '-c:v', video_codec,  # 使用指定的软件编码器
                                '-c:a', 'copy',       # 复制音频流
                                output_path
                            ])
                    else:
                        # 没有指定编码器，使用默认设置
                        if use_gpu and gpu_device != "auto" and gpu_device.startswith("cuda:"):
                            # 使用NVIDIA GPU加速
                            self.logger(f"使用NVIDIA GPU加速转换视频格式")
                            convert_cmd.extend([
                                '-c:v', 'h264_nvenc',  # 使用NVIDIA硬件编码
                                '-preset', 'fast',     # 使用快速预设
                                '-c:a', 'copy',        # 复制音频流
                                output_path
                            ])
                        else:
                            # 不使用GPU，直接复制流
                            convert_cmd.extend(['-c', 'copy', output_path])

                    # 如果设置了内存限制，添加内存限制参数
                    if memory_limit > 0:
                        # 转换为MB
                        mem_limit_bytes = memory_limit * 1024 * 1024
                        convert_cmd.insert(1, "-max_memory")
                        convert_cmd.insert(2, str(mem_limit_bytes))

                    convert_result = subprocess.run(convert_cmd, capture_output=True)

                    if convert_result.returncode != 0:
                        # 如果转换失败，回退到直接复制
                        self.logger(f"格式转换失败，直接复制文件: {str(convert_result.stderr)}")
                        shutil.copy2(video_path, output_path)
                except Exception as e:
                    self.logger(f"格式转换失败，直接复制文件: {str(e)}")
                    shutil.copy2(video_path, output_path)
            else:
                # 直接复制视频文件
                shutil.copy2(video_path, output_path)

            return {
                'success': True,
                'message': f"✅ 视频 {video_file} 处理成功，已保存为: {truncated_name}",
                'file': video_file
            }

        except Exception as e:
            return {
                'success': False,
                'message': f"处理视频 {video_file} 失败: {str(e)}"
            }
