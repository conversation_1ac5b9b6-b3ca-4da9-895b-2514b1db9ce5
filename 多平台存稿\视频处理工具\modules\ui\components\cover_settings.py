"""
封面设置组件 - 封面处理相关设置
"""

import os
import tkinter as tk
from tkinter import ttk, colorchooser
from typing import Dict, List, Any, Callable

def create_cover_settings(parent, ui_instance):
    """创建封面设置面板"""
    # 创建封面设置框架
    settings_frame = ttk.LabelFrame(parent, text="封面设置")
    settings_frame.pack(fill=tk.X, padx=5, pady=5)

    # 第一行: 封面文字设置
    text_frame = ttk.Frame(settings_frame)
    text_frame.pack(fill=tk.X, padx=10, pady=8)

    # 自动使用文件名复选框
    auto_filename_check = ttk.Checkbutton(text_frame, text="自动使用文件名作为封面文字",
                                        variable=ui_instance.auto_use_filename)
    auto_filename_check.pack(side=tk.LEFT)

    # 字体大小设置
    ttk.Label(text_frame, text="字体大小:").pack(side=tk.LEFT, padx=(20, 0))
    font_size_spinner = ttk.Spinbox(text_frame, from_=10, to=200, width=5,
                                  textvariable=ui_instance.cover_size)
    font_size_spinner.pack(side=tk.LEFT, padx=5)

    # 第二行: 封面颜色设置
    color_frame = ttk.Frame(settings_frame)
    color_frame.pack(fill=tk.X, padx=10, pady=8)

    # 顶部颜色
    ttk.Label(color_frame, text="顶部颜色:").pack(side=tk.LEFT)

    # 顶部颜色预览
    top_color_preview = tk.Canvas(color_frame, width=30, height=20, bg=ui_instance.cover_color_top.get())
    top_color_preview.pack(side=tk.LEFT, padx=5)

    # 保存预览引用
    ui_instance.color_preview_top = top_color_preview

    # 更新颜色预览
    from ..styles.widgets import update_color_preview
    update_color_preview(top_color_preview, ui_instance.cover_color_top.get())

    # 顶部颜色选择按钮
    top_color_button = ttk.Button(color_frame, text="选择颜色",
                                command=lambda: choose_color(ui_instance, ui_instance.cover_color_top, top_color_preview))
    top_color_button.pack(side=tk.LEFT, padx=5)

    # 底部颜色
    ttk.Label(color_frame, text="底部颜色:").pack(side=tk.LEFT, padx=(20, 0))

    # 底部颜色预览
    bottom_color_preview = tk.Canvas(color_frame, width=30, height=20, bg=ui_instance.cover_color_bottom.get())
    bottom_color_preview.pack(side=tk.LEFT, padx=5)

    # 保存预览引用
    ui_instance.color_preview_bottom = bottom_color_preview

    # 更新颜色预览
    update_color_preview(bottom_color_preview, ui_instance.cover_color_bottom.get())

    # 底部颜色选择按钮
    bottom_color_button = ttk.Button(color_frame, text="选择颜色",
                                   command=lambda: choose_color(ui_instance, ui_instance.cover_color_bottom, bottom_color_preview))
    bottom_color_button.pack(side=tk.LEFT, padx=5)

    # 第三行: 封面分辨率设置
    resolution_frame = ttk.Frame(settings_frame)
    resolution_frame.pack(fill=tk.X, padx=10, pady=8)

    # 自定义分辨率复选框
    custom_res_check = ttk.Checkbutton(resolution_frame, text="自定义封面分辨率",
                                     variable=ui_instance.enable_custom_cover_resolution)
    custom_res_check.pack(side=tk.LEFT)

    # 宽度设置
    ttk.Label(resolution_frame, text="宽度:").pack(side=tk.LEFT, padx=(20, 0))
    width_spinner = ttk.Spinbox(resolution_frame, from_=100, to=3840, width=6,
                              textvariable=ui_instance.cover_width)
    width_spinner.pack(side=tk.LEFT, padx=5)

    # 高度设置
    ttk.Label(resolution_frame, text="高度:").pack(side=tk.LEFT, padx=(10, 0))
    height_spinner = ttk.Spinbox(resolution_frame, from_=100, to=2160, width=6,
                               textvariable=ui_instance.cover_height)
    height_spinner.pack(side=tk.LEFT, padx=5)

    # 预设按钮
    ttk.Button(resolution_frame, text="720p",
             command=lambda: set_resolution(ui_instance, 1280, 720)).pack(side=tk.LEFT, padx=(20, 5))
    ttk.Button(resolution_frame, text="1080p",
             command=lambda: set_resolution(ui_instance, 1920, 1080)).pack(side=tk.LEFT, padx=5)
    ttk.Button(resolution_frame, text="方形",
             command=lambda: set_resolution(ui_instance, 1080, 1080)).pack(side=tk.LEFT, padx=5)

    # 第四行: 水印设置
    watermark_frame = ttk.Frame(settings_frame)
    watermark_frame.pack(fill=tk.X, padx=10, pady=8)

    # 启用水印复选框
    watermark_check = ttk.Checkbutton(watermark_frame, text="添加水印",
                                    variable=ui_instance.enable_watermark)
    watermark_check.pack(side=tk.LEFT)

    # 水印颜色
    ttk.Label(watermark_frame, text="水印颜色:").pack(side=tk.LEFT, padx=(20, 0))

    # 水印颜色预览
    watermark_color_preview = tk.Canvas(watermark_frame, width=30, height=20, bg=ui_instance.watermark_color.get())
    watermark_color_preview.pack(side=tk.LEFT, padx=5)

    # 保存预览引用
    ui_instance.watermark_color_preview = watermark_color_preview

    # 更新颜色预览
    update_color_preview(watermark_color_preview, ui_instance.watermark_color.get())

    # 水印颜色选择按钮
    watermark_color_button = ttk.Button(watermark_frame, text="选择颜色",
                                      command=lambda: choose_color(ui_instance, ui_instance.watermark_color, watermark_color_preview))
    watermark_color_button.pack(side=tk.LEFT, padx=5)

    # 水印透明度
    ttk.Label(watermark_frame, text="透明度:").pack(side=tk.LEFT, padx=(20, 0))
    opacity_scale = ttk.Scale(watermark_frame, from_=0.0, to=1.0, orient="horizontal",
                            variable=ui_instance.watermark_opacity, length=100)
    opacity_scale.pack(side=tk.LEFT, padx=5)

    # 水印位置
    ttk.Label(watermark_frame, text="位置:").pack(side=tk.LEFT, padx=(20, 0))
    position_combo = ttk.Combobox(watermark_frame, textvariable=ui_instance.watermark_position,
                                values=["左上", "右上", "左下", "右下", "中心", "全屏"],
                                state="readonly", width=8)
    position_combo.pack(side=tk.LEFT, padx=5)

    # 添加工具提示
    if hasattr(ui_instance, 'create_tooltip'):
        ui_instance.create_tooltip(auto_filename_check, "启用后将自动使用视频文件名作为封面文字")
        ui_instance.create_tooltip(font_size_spinner, "设置封面文字的字体大小")
        ui_instance.create_tooltip(top_color_button, "选择封面顶部文字的颜色")
        ui_instance.create_tooltip(bottom_color_button, "选择封面底部文字的颜色")
        ui_instance.create_tooltip(custom_res_check, "启用后可以自定义封面图片的分辨率")
        ui_instance.create_tooltip(width_spinner, "设置封面图片的宽度（像素）")
        ui_instance.create_tooltip(height_spinner, "设置封面图片的高度（像素）")
        ui_instance.create_tooltip(watermark_check, "启用后将在封面图片上添加水印")
        ui_instance.create_tooltip(watermark_color_button, "选择水印的颜色")
        ui_instance.create_tooltip(opacity_scale, "设置水印的透明度，0表示完全透明，1表示完全不透明")
        ui_instance.create_tooltip(position_combo, "选择水印在封面上的位置")

    return settings_frame

def choose_color(ui_instance, color_var, preview_canvas):
    """选择颜色"""
    # 获取当前颜色
    current_color = color_var.get()

    # 打开颜色选择对话框
    color_code = colorchooser.askcolor(current_color, title="选择颜色")

    # 如果用户选择了颜色
    if color_code[1]:
        # 更新颜色变量
        color_var.set(color_code[1])

        # 更新预览
        from ..styles.widgets import update_color_preview
        update_color_preview(preview_canvas, color_code[1])

        # 保存配置
        ui_instance.config_manager.save_config()

def set_resolution(ui_instance, width, height):
    """设置封面分辨率"""
    # 启用自定义分辨率
    ui_instance.enable_custom_cover_resolution.set(True)

    # 设置宽度和高度
    ui_instance.cover_width.set(width)
    ui_instance.cover_height.set(height)

    # 记录日志
    ui_instance.log(f"✅ 封面分辨率已设置为 {width}x{height}")

    # 保存配置
    ui_instance.config_manager.save_config()
