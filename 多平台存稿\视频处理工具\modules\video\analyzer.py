"""
视频分析模块 - 分析视频信息
"""

import os
import json
import subprocess
from typing import Dict, Any

def analyze_video(processor, video_path: str, use_gpu: bool = False, gpu_device: str = "auto", memory_limit: int = 0) -> Dict[str, Any]:
    """
    分析视频信息
    
    Args:
        processor: 视频处理器实例
        video_path: 视频文件路径
        use_gpu: 是否使用GPU加速
        gpu_device: GPU设备
        memory_limit: 内存限制(MB)
        
    Returns:
        Dict: 视频信息，包含width、height、duration等
    """
    try:
        # 优先使用ffprobe快速获取视频信息
        try:
            # 使用ffprobe获取视频信息
            cmd = [
                'ffprobe',
                '-v', 'error',
                '-select_streams', 'v:0',
                '-show_entries', 'stream=width,height,duration',
                '-of', 'json',
                video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                video_info = json.loads(result.stdout)
                stream = video_info.get('streams', [{}])[0]

                width = int(stream.get('width', 0))
                height = int(stream.get('height', 0))
                duration_seconds = float(stream.get('duration', 0))

                if width > 0 and height > 0 and duration_seconds > 0:
                    return {
                        'success': True,
                        'width': width,
                        'height': height,
                        'duration': duration_seconds,
                        'aspect_ratio': width / height
                    }
                else:
                    raise Exception("无法从ffprobe获取完整视频信息")
            else:
                raise Exception(f"ffprobe返回错误: {result.stderr}")

        except Exception as ffmpeg_err:
            # 如果ffprobe失败，继续尝试moviepy
            processor.logger(f"ffprobe处理失败，回退到moviepy: {str(ffmpeg_err)}")

            # 使用moviepy作为备选方案
            from moviepy.video.io.VideoFileClip import VideoFileClip

            with VideoFileClip(video_path) as video:
                duration_seconds = video.duration
                width, height = video.size

                return {
                    'success': True,
                    'width': width,
                    'height': height,
                    'duration': duration_seconds,
                    'aspect_ratio': width / height
                }

    except Exception as e:
        return {
            'success': False,
            'message': f"分析视频失败: {str(e)}"
        }
