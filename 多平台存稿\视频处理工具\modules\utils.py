"""
工具模块 - 包含通用的功能函数
"""

import os
import sys
import subprocess
import re
import traceback
from typing import Dict, List, Tuple, Optional, Any, Union

def ensure_tesseract_available() -> bool:
    """确保Tesseract OCR可用"""
    try:
        import pytesseract
        pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
        return True
    except Exception as e:
        print(f"Tesseract设置失败: {str(e)}")
        return False

def ensure_ffmpeg_available() -> bool:
    """确保FFmpeg在系统中可用，如果不可用则尝试使用本地FFmpeg"""
    # 首先尝试将当前目录的ffmpeg添加到PATH中
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    local_ffmpeg_path = os.path.join(current_dir, "ffmpeg", "bin")
    local_ffmpeg_exe = os.path.join(local_ffmpeg_path, "ffmpeg.exe")

    # 如果本地FFmpeg存在，优先使用
    if os.path.exists(local_ffmpeg_exe):
        print(f"✅ 使用本地FFmpeg: {local_ffmpeg_exe}")
        # 确保添加到环境变量PATH的开头，优先级最高
        os.environ["PATH"] = local_ffmpeg_path + os.pathsep + os.environ.get("PATH", "")
        return True

    # 如果本地找不到，再尝试系统命令
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 系统已安装FFmpeg: " + result.stdout.splitlines()[0])
            return True
    except:
        print("⚠️ 系统未安装FFmpeg或不在PATH中，尝试查找其他位置...")

    # 检查多个可能的位置
    possible_paths = []

    # 1. 当前工作目录下的ffmpeg
    cwd_ffmpeg = os.path.join(os.getcwd(), "ffmpeg", "bin")

    # 2. 网易存稿分支目录下的ffmpeg
    branch_ffmpeg = os.path.join(os.getcwd(), "网易存稿分支", "ffmpeg", "bin")

    # 3. 其他可能的位置
    parent_dir = os.path.dirname(current_dir)
    parent_ffmpeg = os.path.join(parent_dir, "ffmpeg", "bin")

    # 添加所有可能的路径
    possible_paths.extend([
        branch_ffmpeg,   # 网易存稿分支/ffmpeg/bin
        cwd_ffmpeg,      # 当前目录/ffmpeg/bin
        parent_ffmpeg    # 父目录/ffmpeg/bin
    ])

    # 尝试所有可能的路径
    for path in possible_paths:
        ffmpeg_exe = os.path.join(path, "ffmpeg.exe")
        print(f"检查路径: {path}")
        if os.path.exists(ffmpeg_exe):
            print(f"✅ 找到本地FFmpeg: {ffmpeg_exe}")
            # 将FFmpeg路径添加到环境变量PATH中，使得子进程可以找到它
            os.environ["PATH"] = path + os.pathsep + os.environ.get("PATH", "")
            # 测试一下是否能用
            try:
                test_result = subprocess.run([ffmpeg_exe, "-version"], capture_output=True, text=True)
                if test_result.returncode == 0:
                    print("✅ FFmpeg测试成功!")
                    return True
                else:
                    print(f"⚠️ FFmpeg测试失败: {test_result.stderr}")
            except Exception as e:
                print(f"⚠️ FFmpeg测试异常: {str(e)}")
                continue

    print("❌ 未找到FFmpeg，某些功能可能无法正常工作")
    print("搜索的路径包括:")
    for path in possible_paths:
        print(f" - {path}")

    return False

def ensure_moviepy_available() -> bool:
    """确保MoviePy库可用"""
    try:
        import moviepy
        from moviepy.video.io.VideoFileClip import VideoFileClip

        # 获取版本信息
        MOVIEPY_VERSION = getattr(moviepy, '__version__', 'unknown')
        print(f"✅ 成功导入MoviePy (版本: {MOVIEPY_VERSION})")
        print(f"✅ 使用导入路径: moviepy.video.io.VideoFileClip")
        return True
    except ImportError as e:
        print(f"❌ 导入MoviePy失败: {str(e)}")
        print("正在尝试安装MoviePy...")

        try:
            # 尝试安装指定版本
            print("安装MoviePy 2.x版本...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "moviepy>=2.0.0"])

            # 再次尝试导入
            try:
                import moviepy
                from moviepy.video.io.VideoFileClip import VideoFileClip
                MOVIEPY_VERSION = getattr(moviepy, '__version__', 'unknown')
                print(f"✅ 安装并导入MoviePy成功 (版本: {MOVIEPY_VERSION})")
                return True
            except ImportError as e2:
                print(f"❌ 安装后导入仍然失败: {str(e2)}")
                return False
        except Exception as install_err:
            print(f"❌ 安装MoviePy时出错: {str(install_err)}")
            return False

def clean_filename(filename: str) -> str:
    """清理文件名，移除特殊字符和随机后缀"""
    # 移除前缀特殊符号
    while filename and filename[0] in '#@*_-+.,:;!?':
        filename = filename[1:]

    # 移除末尾的随机字符串格式如 _4f38e739
    filename = re.sub(r'_[a-zA-Z0-9]{6,10}$', '', filename)

    # 去除多余空格，修复其他潜在问题
    filename = filename.strip()

    return filename

def smart_split_filename(filename: str, max_total_chars: int = 30) -> Tuple[str, str]:
    """智能拆分文件名为两部分，用于封面文字"""
    # 清理文件名
    filename = clean_filename(filename)

    # 首先确保文件名总长度不超过限制
    if len(filename) > max_total_chars:
        filename = filename[:max_total_chars]

    # 如果文件名很短，直接返回
    if len(filename) <= 15:  # 小于15个字符时不拆分
        return filename, ""

    # 查找拆分点 - 优先在标点符号处拆分
    punctuation = ['.', '_', '-', ' ', '、', '，', '。', '：', '；', '!', '?', '！', '？',
                  '(', ')', '[', ']', '{', '}', '（', '）', '【', '】', '「', '」',
                  '《', '》', '〈', '〉', '"', '"', ''', ''', ',', ';', ':', '/', '\\']

    # 寻找中间点附近的分隔符
    mid_point = len(filename) // 2
    best_split_point = mid_point
    min_distance = len(filename)

    # 在文件名中寻找最接近中点的标点符号
    for i, char in enumerate(filename):
        if char in punctuation:
            distance = abs(i - mid_point)
            if distance < min_distance:
                min_distance = distance
                best_split_point = i + 1  # 在标点符号后拆分

    # 如果没有找到合适的标点符号，尝试寻找中文与非中文字符的分界点
    if best_split_point == mid_point:
        for i in range(mid_point - 5, mid_point + 5):
            if 0 < i < len(filename) - 1:
                # 检查是否是中文与非中文的分界
                curr_is_chinese = '\u4e00' <= filename[i] <= '\u9fff'
                next_is_chinese = '\u4e00' <= filename[i+1] <= '\u9fff'
                if curr_is_chinese != next_is_chinese:
                    best_split_point = i + 1
                    break

    # 拆分文件名
    first_part = filename[:best_split_point]
    second_part = filename[best_split_point:]

    return first_part, second_part

def get_color_map() -> Dict[str, Tuple[int, int, int]]:
    """获取颜色映射表"""
    return {
        "#FFFFFF": (255, 255, 255),  # 白色
        "#000000": (0, 0, 0),        # 黑色
        "#FF0000": (255, 0, 0),      # 红色
        "#0000FF": (0, 0, 255),      # 蓝色
        "#00FF00": (0, 255, 0),      # 绿色
        "#FFFF00": (255, 255, 0),    # 黄色
        "#FFA500": (255, 165, 0),    # 橙色
        "#800080": (128, 0, 128),    # 紫色
        "#FFC0CB": (255, 192, 203),  # 粉色
        "#A52A2A": (165, 42, 42),    # 棕色
        "#808080": (128, 128, 128),  # 灰色
        "#00FFFF": (0, 255, 255)     # 青色
    }

def get_color_names() -> Dict[str, str]:
    """获取颜色名称映射表"""
    return {
        "#FFFFFF": "白色",
        "#000000": "黑色",
        "#FF0000": "红色",
        "#0000FF": "蓝色",
        "#00FF00": "绿色",
        "#FFFF00": "黄色",
        "#FFA500": "橙色",
        "#800080": "紫色",
        "#FFC0CB": "粉色",
        "#A52A2A": "棕色",
        "#808080": "灰色",
        "#00FFFF": "青色"
    }

def get_color_options() -> List[str]:
    """获取颜色选项列表"""
    return [
        "#FFFFFF", "#000000", "#FF0000", "#0000FF", "#00FF00", "#FFFF00",
        "#FFA500", "#800080", "#FFC0CB", "#A52A2A", "#808080", "#00FFFF"
    ]

def center_window(window) -> None:
    """
    将窗口居中显示在屏幕上

    Args:
        window: Tkinter窗口对象
    """
    try:
        # 更新窗口信息以获取实际尺寸
        window.update_idletasks()

        # 获取屏幕尺寸 - 使用多种方法确保准确
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        print(f"屏幕尺寸: {screen_width}x{screen_height}")

        # 尝试使用ctypes获取真实屏幕尺寸
        try:
            from ctypes import windll
            user32 = windll.user32
            real_screen_width = user32.GetSystemMetrics(0)
            real_screen_height = user32.GetSystemMetrics(1)
            print(f"系统报告的屏幕尺寸: {real_screen_width}x{real_screen_height}")

            # 如果系统报告的尺寸更大，使用系统报告的尺寸
            if real_screen_width > screen_width:
                screen_width = real_screen_width
            if real_screen_height > screen_height:
                screen_height = real_screen_height
        except Exception as e:
            print(f"获取系统屏幕尺寸失败: {str(e)}")

        # 获取窗口尺寸
        window_width = window.winfo_width()
        window_height = window.winfo_height()
        print(f"窗口初始尺寸: {window_width}x{window_height}")

        # 如果窗口尺寸为0，使用geometry中的尺寸
        if window_width <= 1 or window_height <= 1:
            geometry = window.geometry()
            print(f"窗口geometry: {geometry}")

            if 'x' in geometry:
                # 解析geometry字符串
                if '+' in geometry:
                    size_part = geometry.split('+')[0]
                else:
                    size_part = geometry

                parts = size_part.split('x')
                if len(parts) == 2:
                    try:
                        window_width = int(parts[0])
                        window_height = int(parts[1])
                        print(f"从geometry解析的窗口尺寸: {window_width}x{window_height}")
                    except ValueError:
                        # 如果转换失败，使用默认值
                        window_width = 2000 if window_width <= 1 else window_width
                        window_height = 1600 if window_height <= 1 else window_height
                        print(f"使用默认窗口尺寸: {window_width}x{window_height}")

        # 确保窗口尺寸不为0且不超过屏幕尺寸
        window_width = max(window_width, 100)
        window_width = min(window_width, screen_width)
        window_height = max(window_height, 100)
        window_height = min(window_height, screen_height)
        print(f"调整后的窗口尺寸: {window_width}x{window_height}")

        # 计算居中位置 - 使用整数除法确保精确居中
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        print(f"计算的居中位置: +{x}+{y}")

        # 确保坐标不为负
        x = max(x, 0)
        y = max(y, 0)

        # 设置窗口位置和大小
        geometry_str = f"{window_width}x{window_height}+{x}+{y}"
        print(f"设置窗口geometry: {geometry_str}")
        window.geometry(geometry_str)

        # 强制更新
        window.update_idletasks()

        # 再次检查窗口位置是否正确
        actual_geometry = window.geometry()
        print(f"设置后的窗口geometry: {actual_geometry}")

        # 如果位置仍然不正确，尝试使用其他方法
        if not actual_geometry.endswith(f"+{x}+{y}"):
            print("窗口位置设置可能不正确，尝试其他方法")

            # 方法2: 使用place方法
            try:
                window.place(x=x, y=y, width=window_width, height=window_height)
                print("使用place方法设置窗口位置")
            except Exception as place_err:
                print(f"place方法失败: {str(place_err)}")

            # 方法3: 使用wm_geometry
            try:
                window.wm_geometry(geometry_str)
                print("使用wm_geometry方法设置窗口位置")
            except Exception as wm_err:
                print(f"wm_geometry方法失败: {str(wm_err)}")

        print(f"窗口已居中: {window_width}x{window_height} 位置: +{x}+{y}")
    except Exception as e:
        print(f"居中窗口时出错: {str(e)}")
